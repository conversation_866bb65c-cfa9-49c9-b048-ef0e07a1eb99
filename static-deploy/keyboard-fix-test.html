<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover, user-scalable=no">
    <title>键盘弹出Bug修复测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: #000000;
            color: white;
            min-height: 100vh;
        }
        
        .header {
            position: fixed;
            top: env(safe-area-inset-top, 0);
            left: 0;
            right: 0;
            background: #000000;
            padding: 14px 20px;
            padding-top: calc(14px + env(safe-area-inset-top, 0));
            z-index: 9999;
            border-bottom: 1px solid #333;
            box-sizing: border-box;
        }

        .content {
            padding-top: calc(70px + env(safe-area-inset-top, 0));
            padding: calc(70px + env(safe-area-inset-top, 0)) 20px 20px;
            min-height: 100vh;
            box-sizing: border-box;
        }
        
        .test-container {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .test-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #333;
            border-radius: 8px;
            background: #111;
            color: white;
            font-size: 16px;
            margin: 10px 0;
            box-sizing: border-box;
        }
        
        .test-textarea {
            width: 100%;
            min-height: 200px;
            padding: 12px;
            border: 1px solid #333;
            border-radius: 8px;
            background: #111;
            color: white;
            font-size: 16px;
            resize: vertical;
            box-sizing: border-box;
        }
        
        .status {
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: 600;
        }
        
        .status.success {
            background: rgba(34, 197, 94, 0.2);
            border: 1px solid rgba(34, 197, 94, 0.4);
        }
        
        .status.warning {
            background: rgba(251, 191, 36, 0.2);
            border: 1px solid rgba(251, 191, 36, 0.4);
        }
        
        .status.error {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid rgba(239, 68, 68, 0.4);
        }
        
        .button {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-weight: 600;
        }
        
        .button:hover {
            background: rgba(255,255,255,0.3);
        }
        
        h1, h2, h3 {
            margin-bottom: 12px;
        }
        
        .viewport-info {
            font-family: monospace;
            font-size: 14px;
            background: #111;
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h2>🔧 键盘弹出Bug修复测试</h2>
    </div>
    
    <div class="content">
        <div class="test-container">
            <h3>📱 测试说明</h3>
            <p>这个页面用于测试移动端键盘弹出时头部导航栏是否会向上滑动的问题。</p>
            <div id="status" class="status warning">
                等待测试...
            </div>
        </div>
        
        <div class="test-container">
            <h3>📊 视口信息</h3>
            <div id="viewport-info" class="viewport-info"></div>
        </div>
        
        <div class="test-container">
            <h3>🧪 测试步骤</h3>
            <ol>
                <li>观察当前头部导航栏的位置</li>
                <li>点击下面的输入框开始输入</li>
                <li>观察键盘弹出时头部是否保持固定</li>
                <li>如果头部没有向上滑动，说明修复成功</li>
            </ol>
        </div>
        
        <div class="test-container">
            <h3>📝 测试输入框</h3>
            <input type="text" class="test-input" placeholder="点击这里输入文字，观察头部是否移动..." id="testInput">
            
            <textarea class="test-textarea" placeholder="这是一个多行文本框，也可以用来测试键盘弹出效果..." id="testTextarea"></textarea>
        </div>
        
        <div class="test-container">
            <h3>🔍 实时监控</h3>
            <div id="monitor" class="viewport-info"></div>
            <button class="button" onclick="runTest()">开始监控</button>
            <button class="button" onclick="stopTest()">停止监控</button>
        </div>
        
        <div class="test-container">
            <h3>✅ 预期结果</h3>
            <ul>
                <li><strong>修复成功：</strong>键盘弹出时，头部导航栏保持在屏幕顶部固定位置</li>
                <li><strong>修复失败：</strong>键盘弹出时，头部导航栏向上滑动或消失</li>
            </ul>
        </div>
    </div>

    <script>
        let monitorInterval;
        let initialViewportHeight = window.innerHeight;
        
        function getSafeAreaTop() {
            // 尝试使用CSS env()函数获取
            const testElement = document.createElement('div');
            testElement.style.paddingTop = 'env(safe-area-inset-top)';
            document.body.appendChild(testElement);
            const computedPadding = window.getComputedStyle(testElement).paddingTop;
            document.body.removeChild(testElement);

            if (computedPadding && computedPadding !== '0px') {
                return parseInt(computedPadding);
            }

            // 回退方案
            const userAgent = navigator.userAgent;
            if (/iPhone/.test(userAgent)) {
                return window.screen.height >= 812 ? 44 : 20;
            } else if (/iPad/.test(userAgent)) {
                return 20;
            } else if (/Android/.test(userAgent)) {
                return 24;
            }
            return 0;
        }

        function updateViewportInfo() {
            const safeAreaTop = getSafeAreaTop();
            const info = {
                '屏幕尺寸': `${screen.width} x ${screen.height}`,
                '视口尺寸': `${window.innerWidth} x ${window.innerHeight}`,
                '设备像素比': window.devicePixelRatio,
                '用户代理': navigator.userAgent.includes('Mobile') ? '移动设备' : '桌面设备',
                '状态栏高度': `${safeAreaTop}px`,
                '初始高度': initialViewportHeight,
                '当前高度': window.innerHeight,
                '高度差': initialViewportHeight - window.innerHeight
            };

            document.getElementById('viewport-info').innerHTML =
                Object.entries(info).map(([key, value]) => `${key}: ${value}`).join('<br>');
        }
        
        function updateStatus() {
            const heightDiff = initialViewportHeight - window.innerHeight;
            const statusEl = document.getElementById('status');
            
            if (heightDiff > 150) {
                statusEl.className = 'status warning';
                statusEl.textContent = '🎹 键盘已弹出 - 检查头部是否保持固定';
            } else if (heightDiff > 50) {
                statusEl.className = 'status warning';
                statusEl.textContent = '📱 视口高度变化中...';
            } else {
                statusEl.className = 'status success';
                statusEl.textContent = '✅ 键盘已收起 - 头部应该保持固定';
            }
        }
        
        function runTest() {
            if (monitorInterval) clearInterval(monitorInterval);
            
            monitorInterval = setInterval(() => {
                const monitor = document.getElementById('monitor');
                const header = document.querySelector('.header');
                const headerRect = header.getBoundingClientRect();
                
                const monitorInfo = {
                    '时间': new Date().toLocaleTimeString(),
                    '视口高度': window.innerHeight,
                    '高度变化': initialViewportHeight - window.innerHeight,
                    '头部位置': `top: ${headerRect.top}px`,
                    '头部样式': window.getComputedStyle(header).position,
                    '键盘状态': (initialViewportHeight - window.innerHeight) > 150 ? '已弹出' : '已收起'
                };
                
                monitor.innerHTML = Object.entries(monitorInfo)
                    .map(([key, value]) => `${key}: ${value}`).join('<br>');
                
                updateStatus();
            }, 100);
        }
        
        function stopTest() {
            if (monitorInterval) {
                clearInterval(monitorInterval);
                monitorInterval = null;
            }
        }
        
        // 初始化
        updateViewportInfo();
        updateStatus();
        
        // 监听视口变化
        window.addEventListener('resize', () => {
            updateViewportInfo();
            updateStatus();
        });
        
        // 监听输入框焦点
        document.getElementById('testInput').addEventListener('focus', () => {
            console.log('输入框获得焦点');
        });
        
        document.getElementById('testTextarea').addEventListener('focus', () => {
            console.log('文本框获得焦点');
        });
    </script>
</body>
</html>
