<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .device-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        .test-result {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>📱 移动端优化测试</h1>
        
        <div class="device-info">
            <h3>设备信息</h3>
            <p><strong>用户代理:</strong> <span id="userAgent"></span></p>
            <p><strong>屏幕尺寸:</strong> <span id="screenSize"></span></p>
            <p><strong>视口尺寸:</strong> <span id="viewportSize"></span></p>
            <p><strong>设备类型:</strong> <span id="deviceType"></span></p>
        </div>

        <div class="test-result info">
            <h3>✨ UI重新设计优化测试</h3>
            <p>本页面用于测试移动端UI优化效果：</p>
            <ul>
                <li>✅ 移动端只显示 Essays 按钮（隐藏Blogs和Gallery）</li>
                <li>✅ 移动端工具栏简化（保留基本功能+图片上传）</li>
                <li>✅ 现代化按钮设计（圆角、阴影、动画效果）</li>
                <li>✅ 优化的头部导航栏（毛玻璃效果、更好的间距）</li>
                <li>✅ 改进的编辑器容器（更美观的阴影和圆角）</li>
                <li>✅ 响应式布局优化（更好的移动端体验）</li>
                <li>✅ 桌面端保持完整功能</li>
            </ul>
        </div>

        <div class="test-result success">
            <h3>🎨 新UI特性</h3>
            <ul>
                <li><strong>毛玻璃效果：</strong> 头部导航栏和按钮使用backdrop-filter</li>
                <li><strong>现代化按钮：</strong> 12px圆角、渐变背景、悬停动画</li>
                <li><strong>优雅阴影：</strong> 多层阴影效果，增强视觉层次</li>
                <li><strong>流畅动画：</strong> cubic-bezier缓动函数，更自然的交互</li>
                <li><strong>移动端优化：</strong> 更大的触摸目标，更好的间距</li>
            </ul>
        </div>

        <button class="button" onclick="openEditor()">打开编辑器测试</button>
        <button class="button" onclick="runTests()">运行自动测试</button>
        
        <div id="testResults"></div>
    </div>

    <script>
        // 检测设备类型
        function isMobileDevice() {
            const userAgent = navigator.userAgent.toLowerCase();
            const mobileKeywords = ['mobile', 'android', 'iphone', 'ipad', 'ipod', 'blackberry', 'windows phone'];
            return mobileKeywords.some(keyword => userAgent.includes(keyword)) ||
                   window.innerWidth <= 768;
        }

        // 更新设备信息
        function updateDeviceInfo() {
            document.getElementById('userAgent').textContent = navigator.userAgent;
            document.getElementById('screenSize').textContent = `${screen.width} x ${screen.height}`;
            document.getElementById('viewportSize').textContent = `${window.innerWidth} x ${window.innerHeight}`;
            document.getElementById('deviceType').textContent = isMobileDevice() ? '移动设备' : '桌面设备';
        }

        // 打开编辑器
        function openEditor() {
            window.open('./index.html', '_blank');
        }

        // 运行测试
        function runTests() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<h3>测试结果</h3>';
            
            const isMobile = isMobileDevice();
            
            // 测试1: 设备检测
            const test1 = document.createElement('div');
            test1.className = 'test-result success';
            test1.innerHTML = `<strong>✓ 设备检测:</strong> 当前识别为${isMobile ? '移动' : '桌面'}设备`;
            resultsDiv.appendChild(test1);
            
            // 测试2: 屏幕尺寸
            const test2 = document.createElement('div');
            test2.className = window.innerWidth <= 768 ? 'test-result success' : 'test-result info';
            test2.innerHTML = `<strong>${window.innerWidth <= 768 ? '✓' : 'ℹ'} 屏幕尺寸:</strong> ${window.innerWidth}px ${window.innerWidth <= 768 ? '(移动端尺寸)' : '(桌面端尺寸)'}`;
            resultsDiv.appendChild(test2);
            
            // 测试3: CSS媒体查询
            const test3 = document.createElement('div');
            const mediaQuery = window.matchMedia('(max-width: 768px)');
            test3.className = 'test-result success';
            test3.innerHTML = `<strong>✓ CSS媒体查询:</strong> max-width: 768px ${mediaQuery.matches ? '匹配' : '不匹配'}`;
            resultsDiv.appendChild(test3);
            
            // 建议
            const suggestion = document.createElement('div');
            suggestion.className = 'test-result warning';
            suggestion.innerHTML = `<strong>💡 建议:</strong> 请在${isMobile ? '桌面' : '移动'}设备上也测试一下，确保两端都正常工作。`;
            resultsDiv.appendChild(suggestion);
        }

        // 页面加载时更新信息
        window.addEventListener('load', updateDeviceInfo);
        window.addEventListener('resize', updateDeviceInfo);
    </script>
</body>
</html>
