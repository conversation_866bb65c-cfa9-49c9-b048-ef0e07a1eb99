<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover, user-scalable=no">
    <title>BlogWriter - GitHub Auto Deploy</title>
    <meta name="version" content="1.2">

    <!-- 移动设备状态栏样式 -->
    <meta name="theme-color" content="#000000">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="msapplication-navbutton-color" content="#000000">

    <link rel="icon" type="image/png" href="./img/icons/write.png">
    <script src="https://unpkg.com/vue@2.6.14/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui@2.15.13/lib/index.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/element-ui@2.15.13/lib/theme-chalk/index.css">
    <script src="https://cdn.jsdelivr.net/npm/vditor@3.9.4/dist/index.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/vditor@3.9.4/dist/index.css">
    <script src="github-service.js"></script>
    <script src="image-service.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            transition: all 0.3s ease;
        }

        /* 主题系统 */

        /* 暗夜模式样式 */
        .theme-dark {
            background: #000000 !important;
            color: #e0e0e0;
        }

        .theme-dark .header {
            background: #000000 !important;
            border-bottom-color: #888;
        }

        .theme-dark .content-type-selector {
            background: #000000;
        }

        /* 深灰色主题 */
        .theme-gray {
            background: #2d3748 !important;
            color: #e2e8f0;
        }

        .theme-gray .header {
            background: #1a202c !important;
            border-bottom-color: #4a5568;
        }

        .theme-gray .content-type-selector {
            background: #1a202c;
        }

        /* 纸黄色主题 */
        .theme-paper {
            background: #fef7e0 !important;
            color: #744210;
        }

        .theme-paper .header {
            background: #f6e05e !important;
            border-bottom-color: #d69e2e;
            color: #744210 !important;
        }

        .theme-paper .content-type-selector {
            background: #f6e05e;
        }

        /* 森林绿主题 */
        .theme-forest {
            background: #1a2e1a !important;
            color: #c6f6d5;
        }

        .theme-forest .header {
            background: #22543d !important;
            border-bottom-color: #38a169;
        }

        .theme-forest .content-type-selector {
            background: #22543d;
        }

        /* 海洋蓝主题 */
        .theme-ocean {
            background: #1a365d !important;
            color: #bee3f8;
        }

        .theme-ocean .header {
            background: #2c5282 !important;
            border-bottom-color: #3182ce;
        }

        .theme-ocean .content-type-selector {
            background: #2c5282;
        }

        /* 主要按钮样式 - 适用于所有主题 */
        .theme-dark .content-type-selector .el-button--primary,
        .theme-dark .el-button--primary,
        .theme-gray .content-type-selector .el-button--primary,
        .theme-gray .el-button--primary,
        .theme-forest .content-type-selector .el-button--primary,
        .theme-forest .el-button--primary,
        .theme-ocean .content-type-selector .el-button--primary,
        .theme-ocean .el-button--primary {
            background: #002FA7 !important;
            border-color: #002FA7 !important;
            color: white !important;
        }

        .theme-dark .content-type-selector .el-button--primary:hover,
        .theme-dark .el-button--primary:hover,
        .theme-gray .content-type-selector .el-button--primary:hover,
        .theme-gray .el-button--primary:hover,
        .theme-forest .content-type-selector .el-button--primary:hover,
        .theme-forest .el-button--primary:hover,
        .theme-ocean .content-type-selector .el-button--primary:hover,
        .theme-ocean .el-button--primary:hover {
            background: #0033B8 !important;
            border-color: #0033B8 !important;
            color: white !important;
        }

        /* 纸黄色主题的按钮样式 */
        .theme-paper .content-type-selector .el-button--primary,
        .theme-paper .el-button--primary {
            background: #d69e2e !important;
            border-color: #d69e2e !important;
            color: white !important;
        }

        .theme-paper .content-type-selector .el-button--primary:hover,
        .theme-paper .el-button--primary:hover {
            background: #b7791f !important;
            border-color: #b7791f !important;
            color: white !important;
        }

        /* 强制覆盖Element UI的默认样式 */
        .theme-dark .content-type-selector .el-button.el-button--primary,
        .theme-gray .content-type-selector .el-button.el-button--primary,
        .theme-forest .content-type-selector .el-button.el-button--primary,
        .theme-ocean .content-type-selector .el-button.el-button--primary {
            background-color: #002FA7 !important;
            border-color: #002FA7 !important;
        }

        .theme-dark .content-type-selector .el-button.el-button--primary:hover,
        .theme-gray .content-type-selector .el-button.el-button--primary:hover,
        .theme-forest .content-type-selector .el-button.el-button--primary:hover,
        .theme-ocean .content-type-selector .el-button.el-button--primary:hover {
            background-color: #0033B8 !important;
            border-color: #0033B8 !important;
        }

        .theme-paper .content-type-selector .el-button.el-button--primary {
            background-color: #d69e2e !important;
            border-color: #d69e2e !important;
        }

        .theme-paper .content-type-selector .el-button.el-button--primary:hover {
            background-color: #b7791f !important;
            border-color: #b7791f !important;
        }

        /* 发布按钮样式 */
        .publish-button {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
            border: none !important;
            color: white !important;
            font-weight: 600 !important;
            padding: 12px 20px !important;
            border-radius: 12px !important;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3) !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        }

        .publish-button:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%) !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4) !important;
        }

        .publish-button:disabled {
            background: #9ca3af !important;
            box-shadow: none !important;
            transform: none !important;
            cursor: not-allowed !important;
        }

        /* 发布按钮图标大小 */
        .publish-button .el-icon-upload {
            font-size: 18px !important;
        }

        /* 发布按钮成功动画 */
        .publish-button.success {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%) !important;
            animation: successPulse 0.6s ease-out;
        }

        /* 移动端发布按钮样式 */
        .mobile-publish-button {
            padding: 8px 12px !important;
            font-size: 14px !important;
            border-radius: 12px !important;
            min-width: auto !important;
        }

        @keyframes successPulse {
            0% {
                transform: scale(1);
                box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
            }
            50% {
                transform: scale(1.05);
                box-shadow: 0 4px 16px rgba(76, 175, 80, 0.6);
            }
            100% {
                transform: scale(1);
                box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
            }
        }

        /* GitHub配置按钮 */
        .github-config-button {
            width: 44px;
            height: 44px;
            border-radius: 12px;
            border: none;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            background: rgba(255,255,255,0.15);
            color: white;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .github-config-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.2);
            background: rgba(255,255,255,0.25);
        }

        .github-config-button .el-icon-setting {
            font-size: 18px;
        }

        /* 自定义图标样式 */
        .github-config-button img {
            transition: all 0.3s ease;
            /* 将黑色线条转换为白色 */
            filter: invert(1) brightness(0.9);
        }

        .github-config-button:hover img {
            filter: invert(1) brightness(1.1) drop-shadow(0 0 3px rgba(255,255,255,0.3));
            transform: scale(1.05);
        }

        /* Vditor YAML frontmatter 预览样式 */
        .vditor-preview .language-yaml,
        .vditor-preview pre[data-lang="yaml"],
        .vditor-preview code.language-yaml {
            background: #002FA7 !important;
            color: white !important;
            border: 1px solid #0033B8 !important;
        }

        /* Vditor 编辑器中的 YAML 代码块样式 */
        .vditor-ir pre.vditor-ir__marker--pre code,
        .vditor-wysiwyg pre code {
            background: #002FA7 !important;
            color: white !important;
        }

        /* 针对 frontmatter 的特殊样式 */
        .vditor-preview hr + pre,
        .vditor-preview hr + pre code {
            background: #002FA7 !important;
            color: white !important;
            border: 2px solid #0033B8 !important;
            border-radius: 6px !important;
        }

        /* 更强制的样式覆盖 - 针对所有可能的YAML显示 */
        .vditor-preview pre,
        .vditor-preview pre code,
        .vditor-preview .hljs,
        .vditor-preview .hljs-attr,
        .vditor-preview .hljs-string,
        .vditor-preview .hljs-literal {
            background: #002FA7 !important;
            color: white !important;
        }

        /* 确保YAML语法高亮也使用克莱因蓝 */
        .vditor-preview .hljs-attr {
            color: #FFD700 !important; /* 金色用于属性名 */
        }

        .vditor-preview .hljs-string {
            color: #90EE90 !important; /* 浅绿色用于字符串值 */
        }

        .vditor-preview .hljs-literal {
            color: #FFA500 !important; /* 橙色用于字面量 */
        }

        /* 编辑器容器主题样式 */
        .theme-dark .editor-container {
            background: #000000 !important;
            border-color: #333333 !important;
            box-shadow: 0 4px 20px rgba(255,255,255,0.05);
        }
        .theme-dark #vditor,
        .theme-dark .vditor {
            background: #000000 !important;
        }

        .theme-gray .editor-container {
            background: #2d3748 !important;
            border-color: #4a5568 !important;
            box-shadow: 0 4px 20px rgba(255,255,255,0.05);
        }
        .theme-gray #vditor,
        .theme-gray .vditor {
            background: #2d3748 !important;
        }

        .theme-paper .editor-container {
            background: #fef7e0 !important;
            border-color: #d69e2e !important;
            box-shadow: 0 4px 20px rgba(214,158,46,0.2);
        }
        .theme-paper #vditor,
        .theme-paper .vditor {
            background: #fef7e0 !important;
        }

        .theme-forest .editor-container {
            background: #1a2e1a !important;
            border-color: #38a169 !important;
            box-shadow: 0 4px 20px rgba(56,161,105,0.2);
        }
        .theme-forest #vditor,
        .theme-forest .vditor {
            background: #1a2e1a !important;
        }

        .theme-ocean .editor-container {
            background: #1a365d !important;
            border-color: #3182ce !important;
            box-shadow: 0 4px 20px rgba(49,130,206,0.2);
        }
        .theme-ocean #vditor,
        .theme-ocean .vditor {
            background: #1a365d !important;
        }

        .header {
            border-bottom: none;
            padding: 20px 28px;
            box-shadow: 0 6px 30px rgba(0,0,0,0.15);
            color: white;
            /* 默认渐变背景，会被主题样式覆盖 */
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: sticky;
            top: 0;
            z-index: 1000;
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        /* 桌面端保持sticky定位 */
        @media (min-width: 769px) {
            .header {
                position: sticky !important;
            }
        }
        .content-type-selector {
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        .type-buttons {
            display: flex;
            gap: 14px;
            align-items: center;
        }
        .action-buttons {
            display: flex;
            gap: 14px;
            align-items: center;
        }

        /* 美化按钮样式 - 现代化按钮 */
        .el-button {
            border-radius: 12px !important;
            font-weight: 600 !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
            padding: 12px 24px !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
            backdrop-filter: blur(10px) !important;
        }

        .el-button:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 4px 16px rgba(0,0,0,0.15) !important;
        }

        .el-button:active {
            transform: translateY(0) !important;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
        }

        .el-button:hover {
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
        }

        .el-button.is-circle {
            border-radius: 50% !important;
            width: 40px !important;
            height: 40px !important;
            padding: 0 !important;
        }
        .editor-container {
            height: calc(100vh - 130px);
            padding: 0;
            background: #fafafa;
            margin: 24px auto;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0,0,0,0.12), 0 2px 8px rgba(0,0,0,0.08);
            border: 1px solid rgba(255,255,255,0.2);
            max-width: 1200px;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .editor-container:hover {
            box-shadow: 0 12px 40px rgba(0,0,0,0.15), 0 4px 12px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        .metadata-editor {
            display: flex;
            gap: 15px;
            padding: 10px 15px;
            border-bottom: 1px solid #30363d;
            position: sticky;
            top: 41px; /* Vditor toolbar height */
            z-index: 2;
        }

        /* 元数据编辑器主题样式 */
        .theme-dark .metadata-editor {
            background-color: #000000;
            border-bottom-color: #30363d;
        }

        .theme-gray .metadata-editor {
            background-color: #1a202c;
            border-bottom-color: #4a5568;
        }

        .theme-paper .metadata-editor {
            background-color: #f6e05e;
            border-bottom-color: #d69e2e;
        }

        .theme-forest .metadata-editor {
            background-color: #22543d;
            border-bottom-color: #38a169;
        }

        .theme-ocean .metadata-editor {
            background-color: #2c5282;
            border-bottom-color: #3182ce;
        }
        .metadata-editor .el-input {
            flex: 1;
        }
        .metadata-editor .el-input[placeholder="文章标题"] {
            flex: 2; /* 标题框更宽 */
        }
        .metadata-editor .el-input__inner {
            height: 32px;
            line-height: 32px;
        }
        /* 输入框主题样式 */
        .theme-dark .el-input__inner {
            background-color: #1a1a1a !important;
            border-color: #555 !important;
            color: #e0e0e0 !important;
        }
        .theme-dark .el-input__inner:hover, .theme-dark .el-input__inner:focus {
            border-color: #002FA7 !important;
        }

        .theme-gray .el-input__inner {
            background-color: #2d3748 !important;
            border-color: #4a5568 !important;
            color: #e2e8f0 !important;
        }
        .theme-gray .el-input__inner:hover, .theme-gray .el-input__inner:focus {
            border-color: #002FA7 !important;
        }

        .theme-paper .el-input__inner {
            background-color: #fffbeb !important;
            border-color: #d69e2e !important;
            color: #744210 !important;
        }
        .theme-paper .el-input__inner:hover, .theme-paper .el-input__inner:focus {
            border-color: #b7791f !important;
        }

        .theme-forest .el-input__inner {
            background-color: #22543d !important;
            border-color: #38a169 !important;
            color: #c6f6d5 !important;
        }
        .theme-forest .el-input__inner:hover, .theme-forest .el-input__inner:focus {
            border-color: #48bb78 !important;
        }

        .theme-ocean .el-input__inner {
            background-color: #2c5282 !important;
            border-color: #3182ce !important;
            color: #bee3f8 !important;
        }
        .theme-ocean .el-input__inner:hover, .theme-ocean .el-input__inner:focus {
            border-color: #4299e1 !important;
        }
        #vditor {
            height: 100%;
            border-radius: 0;
            border: none !important;
        }

        /* 编辑器内容区域内边距 */
        .vditor-ir .vditor-ir__main,
        .vditor-wysiwyg .vditor-wysiwyg__main,
        .vditor-sv .vditor-sv__main {
            padding: 30px 60px !important;
            max-width: none !important;
        }

        .vditor-preview {
            padding: 30px 60px !important;
            max-width: none !important;
        }

        /* 编辑器文本区域 */
        .vditor-ir .vditor-ir__main textarea {
            padding: 0 !important;
            margin: 0 !important;
        }

        /* 编辑器字体大小 */
        .vditor-sv .vditor-sv__main textarea,
        .vditor-wysiwyg .vditor-wysiwyg__main {
            font-size: 16px !important;
            line-height: 1.6 !important;
        }

        /* Vditor编辑器主题样式 */

        /* 暗夜主题 */
        .theme-dark .vditor-toolbar {
            background: #000000 !important;
            border-bottom-color: #888 !important;
        }

        .theme-dark .vditor-content,
        .theme-dark .vditor-wysiwyg,
        .theme-dark .vditor-wysiwyg .vditor-wysiwyg__main {
            background: #000000 !important;
            color: #e0e0e0 !important;
        }

        /* 深灰主题 */
        .theme-gray .vditor-toolbar {
            background: #1a202c !important;
            border-bottom-color: #4a5568 !important;
        }

        .theme-gray .vditor-content,
        .theme-gray .vditor-wysiwyg,
        .theme-gray .vditor-wysiwyg .vditor-wysiwyg__main {
            background: #2d3748 !important;
            color: #e2e8f0 !important;
        }

        /* 纸黄主题 */
        .theme-paper .vditor-toolbar {
            background: #f6e05e !important;
            border-bottom-color: #d69e2e !important;
        }

        .theme-paper .vditor-content,
        .theme-paper .vditor-wysiwyg,
        .theme-paper .vditor-wysiwyg .vditor-wysiwyg__main {
            background: #fef7e0 !important;
            color: #744210 !important;
        }

        /* 森林主题 */
        .theme-forest .vditor-toolbar {
            background: #22543d !important;
            border-bottom-color: #38a169 !important;
        }

        .theme-forest .vditor-content,
        .theme-forest .vditor-wysiwyg,
        .theme-forest .vditor-wysiwyg .vditor-wysiwyg__main {
            background: #1a2e1a !important;
            color: #c6f6d5 !important;
        }

        /* 海洋主题 */
        .theme-ocean .vditor-toolbar {
            background: #2c5282 !important;
            border-bottom-color: #3182ce !important;
        }

        .theme-ocean .vditor-content,
        .theme-ocean .vditor-wysiwyg,
        .theme-ocean .vditor-wysiwyg .vditor-wysiwyg__main {
            background: #1a365d !important;
            color: #bee3f8 !important;
        }

        /* 详细的编辑器模式样式 */

        /* 暗夜主题 - 详细样式 */
        .theme-dark .vditor-wysiwyg__main,
        .theme-dark .vditor-wysiwyg__main .vditor-wysiwyg__main,
        .theme-dark .vditor-wysiwyg .vditor-wysiwyg__main .vditor-wysiwyg__main,
        .theme-dark .vditor-wysiwyg__main > div,
        .theme-dark .vditor-wysiwyg__main .vditor-wysiwyg__main > div,
        .theme-dark .vditor-wysiwyg__main[contenteditable="true"],
        .theme-dark .vditor-ir,
        .theme-dark .vditor-ir .vditor-ir__main,
        .theme-dark .vditor-ir .vditor-ir__main textarea {
            background: #000000 !important;
            background-color: #000000 !important;
            color: #e0e0e0 !important;
        }

        /* 深灰主题 - 详细样式 */
        .theme-gray .vditor-wysiwyg__main,
        .theme-gray .vditor-wysiwyg__main .vditor-wysiwyg__main,
        .theme-gray .vditor-wysiwyg .vditor-wysiwyg__main .vditor-wysiwyg__main,
        .theme-gray .vditor-wysiwyg__main > div,
        .theme-gray .vditor-wysiwyg__main .vditor-wysiwyg__main > div,
        .theme-gray .vditor-wysiwyg__main[contenteditable="true"],
        .theme-gray .vditor-ir,
        .theme-gray .vditor-ir .vditor-ir__main,
        .theme-gray .vditor-ir .vditor-ir__main textarea {
            background: #2d3748 !important;
            background-color: #2d3748 !important;
            color: #e2e8f0 !important;
        }

        /* 纸黄主题 - 详细样式 */
        .theme-paper .vditor-wysiwyg__main,
        .theme-paper .vditor-wysiwyg__main .vditor-wysiwyg__main,
        .theme-paper .vditor-wysiwyg .vditor-wysiwyg__main .vditor-wysiwyg__main,
        .theme-paper .vditor-wysiwyg__main > div,
        .theme-paper .vditor-wysiwyg__main .vditor-wysiwyg__main > div,
        .theme-paper .vditor-wysiwyg__main[contenteditable="true"],
        .theme-paper .vditor-ir,
        .theme-paper .vditor-ir .vditor-ir__main,
        .theme-paper .vditor-ir .vditor-ir__main textarea {
            background: #fef7e0 !important;
            background-color: #fef7e0 !important;
            color: #744210 !important;
        }

        /* 森林主题 - 详细样式 */
        .theme-forest .vditor-wysiwyg__main,
        .theme-forest .vditor-wysiwyg__main .vditor-wysiwyg__main,
        .theme-forest .vditor-wysiwyg .vditor-wysiwyg__main .vditor-wysiwyg__main,
        .theme-forest .vditor-wysiwyg__main > div,
        .theme-forest .vditor-wysiwyg__main .vditor-wysiwyg__main > div,
        .theme-forest .vditor-wysiwyg__main[contenteditable="true"],
        .theme-forest .vditor-ir,
        .theme-forest .vditor-ir .vditor-ir__main,
        .theme-forest .vditor-ir .vditor-ir__main textarea {
            background: #1a2e1a !important;
            background-color: #1a2e1a !important;
            color: #c6f6d5 !important;
        }

        /* 海洋主题 - 详细样式 */
        .theme-ocean .vditor-wysiwyg__main,
        .theme-ocean .vditor-wysiwyg__main .vditor-wysiwyg__main,
        .theme-ocean .vditor-wysiwyg .vditor-wysiwyg__main .vditor-wysiwyg__main,
        .theme-ocean .vditor-wysiwyg__main > div,
        .theme-ocean .vditor-wysiwyg__main .vditor-wysiwyg__main > div,
        .theme-ocean .vditor-wysiwyg__main[contenteditable="true"],
        .theme-ocean .vditor-ir,
        .theme-ocean .vditor-ir .vditor-ir__main,
        .theme-ocean .vditor-ir .vditor-ir__main textarea {
            background: #1a365d !important;
            background-color: #1a365d !important;
            color: #bee3f8 !important;
        }

        /* 预览区域和分屏模式样式 */

        /* 暗夜主题 - 预览和分屏 */
        .theme-dark .vditor-preview,
        .theme-dark .vditor-preview .vditor-reset,
        .theme-dark .vditor-sv,
        .theme-dark .vditor-sv .vditor-sv__main,
        .theme-dark .vditor-sv .vditor-sv__preview,
        .theme-dark .vditor-sv .vditor-sv__preview .vditor-reset {
            background: #000000 !important;
            color: #e0e0e0 !important;
        }

        /* 深灰主题 - 预览和分屏 */
        .theme-gray .vditor-preview,
        .theme-gray .vditor-preview .vditor-reset,
        .theme-gray .vditor-sv,
        .theme-gray .vditor-sv .vditor-sv__main,
        .theme-gray .vditor-sv .vditor-sv__preview,
        .theme-gray .vditor-sv .vditor-sv__preview .vditor-reset {
            background: #2d3748 !important;
            color: #e2e8f0 !important;
        }

        /* 纸黄主题 - 预览和分屏 */
        .theme-paper .vditor-preview,
        .theme-paper .vditor-preview .vditor-reset,
        .theme-paper .vditor-sv,
        .theme-paper .vditor-sv .vditor-sv__main,
        .theme-paper .vditor-sv .vditor-sv__preview,
        .theme-paper .vditor-sv .vditor-sv__preview .vditor-reset {
            background: #fef7e0 !important;
            color: #744210 !important;
        }

        /* 森林主题 - 预览和分屏 */
        .theme-forest .vditor-preview,
        .theme-forest .vditor-preview .vditor-reset,
        .theme-forest .vditor-sv,
        .theme-forest .vditor-sv .vditor-sv__main,
        .theme-forest .vditor-sv .vditor-sv__preview,
        .theme-forest .vditor-sv .vditor-sv__preview .vditor-reset {
            background: #1a2e1a !important;
            color: #c6f6d5 !important;
        }

        /* 海洋主题 - 预览和分屏 */
        .theme-ocean .vditor-preview,
        .theme-ocean .vditor-preview .vditor-reset,
        .theme-ocean .vditor-sv,
        .theme-ocean .vditor-sv .vditor-sv__main,
        .theme-ocean .vditor-sv .vditor-sv__preview,
        .theme-ocean .vditor-sv .vditor-sv__preview .vditor-reset {
            background: #1a365d !important;
            color: #bee3f8 !important;
        }

        /* 强制覆盖所有可能的Vditor背景样式 */
        .theme-dark .vditor-wysiwyg *,
        .theme-dark .vditor-ir *,
        .theme-gray .vditor-wysiwyg *,
        .theme-gray .vditor-ir *,
        .theme-paper .vditor-wysiwyg *,
        .theme-paper .vditor-ir *,
        .theme-forest .vditor-wysiwyg *,
        .theme-forest .vditor-ir *,
        .theme-ocean .vditor-wysiwyg *,
        .theme-ocean .vditor-ir * {
            background-color: transparent !important;
        }

        /* 隐藏预览工具栏的Desktop/Tablet/Mobile等选项 */
        .vditor-preview__action,
        .vditor-preview__action--current,
        .vditor-preview__action[data-type="desktop"],
        .vditor-preview__action[data-type="tablet"],
        .vditor-preview__action[data-type="mobile"],
        .vditor-preview__action[data-type="mp-wechat"],
        .vditor-preview__action[data-type="zhihu"] {
            display: none !important;
        }

        /* 隐藏整个预览工具栏 */
        .vditor-preview__toolbar {
            display: none !important;
        }

        /* 美化GitHub配置弹窗 */
        .el-dialog, .el-dialog__wrapper {
            background: transparent !important;
        }

        .github-config-dialog .el-dialog {
            background: #161b22 !important;
            border-radius: 25px !important;
            border: 1px solid #30363d !important;
            box-shadow: 0 8px 24px rgba(0,0,0,0.5) !important;
        }

        .github-config-dialog .el-dialog__header {
            background: #161b22 !important;
            color: #c9d1d9 !important;
            border-bottom: 1px solid #30363d !important;
        }

        .github-config-dialog .el-dialog__title {
            font-size: 18px !important;
            font-weight: 600;
            color: #e0e0e0 !important;
        }

        .github-config-dialog .el-dialog__body {
            background: #0d1117 !important;
            color: #c9d1d9 !important;
            padding: 25px 30px !important;
        }

        .github-config-dialog .el-form-item__label {
            color: #c9d1d9 !important;
            white-space: nowrap;
        }

        .github-config-dialog .el-input__inner {
            background: #010409 !important;
            border: 1px solid #30363d !important;
            color: #c9d1d9 !important;
            border-radius: 20px !important;
        }

        .github-config-dialog .el-input__inner:focus {
            border-color: #002FA7 !important;
            box-shadow: 0 0 0 3px rgba(0, 47, 167, 0.3) !important;
        }

        .github-config-dialog .token-instructions {
            font-size: 12px;
            color: #8b949e;
            margin-top: 8px;
            line-height: 1.6;
            background: #161b22;
            padding: 10px;
            border-radius: 6px;
        }

        .github-config-dialog .el-dialog__footer {
            background: #161b22 !important;
            border-top: 1px solid #30363d !important;
            padding: 15px 30px !important;
        }

        .github-config-dialog .test-result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 6px;
            font-weight: 500;
        }

        .github-config-dialog .test-result.success {
            background-color: rgba(34, 139, 34, 0.2);
            color: #3fb950;
            border: 1px solid rgba(34, 139, 34, 0.5);
        }

        .github-config-dialog .test-result.error {
            background-color: rgba(210, 4, 45, 0.2);
            color: #f85149;
            border: 1px solid rgba(210, 4, 45, 0.5);
        }

        .metadata-info {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            margin-left: 10px;
            background: rgba(255, 255, 255, 0.1);
            padding: 4px 8px;
            border-radius: 12px;
            backdrop-filter: blur(10px);
        }

        /* 元数据信息样式 */
        .theme-dark .metadata-info,
        .theme-gray .metadata-info,
        .theme-forest .metadata-info,
        .theme-ocean .metadata-info {
            color: rgba(224, 224, 224, 0.8);
            background: rgba(255, 255, 255, 0.1);
        }

        .theme-paper .metadata-info {
            color: rgba(116, 66, 16, 0.8);
            background: rgba(116, 66, 16, 0.1);
        }

        /* 保存按钮克莱因蓝样式 - 更强制的选择器 */
        .save-button,
        .el-dialog .save-button,
        .el-dialog__footer .save-button,
        .el-dialog .el-button.save-button,
        .el-dialog__footer .el-button.save-button,
        button.save-button,
        .el-button--primary.save-button {
            background: #002FA7 !important;
            background-color: #002FA7 !important;
            border-color: #002FA7 !important;
            color: white !important;
            box-shadow: none !important;
        }

        .save-button:hover,
        .el-dialog .save-button:hover,
        .el-dialog__footer .save-button:hover,
        .el-dialog .el-button.save-button:hover,
        .el-dialog__footer .el-button.save-button:hover,
        button.save-button:hover,
        .el-button--primary.save-button:hover {
            background: #0033B8 !important;
            background-color: #0033B8 !important;
            border-color: #0033B8 !important;
            color: white !important;
            box-shadow: none !important;
        }

        .save-button:focus,
        .save-button:active,
        .el-dialog .save-button:focus,
        .el-dialog .save-button:active,
        .el-dialog__footer .save-button:focus,
        .el-dialog__footer .save-button:active,
        .el-dialog .el-button.save-button:focus,
        .el-dialog .el-button.save-button:active,
        .el-dialog__footer .el-button.save-button:focus,
        .el-dialog__footer .el-button.save-button:active,
        button.save-button:focus,
        button.save-button:active,
        .el-button--primary.save-button:focus,
        .el-button--primary.save-button:active {
            background: #002FA7 !important;
            background-color: #002FA7 !important;
            border-color: #002FA7 !important;
            color: white !important;
            box-shadow: none !important;
        }

        /* 通用按钮样式 */
        .theme-dark .el-button,
        .theme-gray .el-button,
        .theme-forest .el-button,
        .theme-ocean .el-button {
            background: #333 !important;
            border-color: #555 !important;
            color: #e0e0e0 !important;
        }

        .theme-dark .el-button:hover,
        .theme-gray .el-button:hover,
        .theme-forest .el-button:hover,
        .theme-ocean .el-button:hover {
            background: #444 !important;
            border-color: #666 !important;
        }

        .theme-paper .el-button {
            background: #f7fafc !important;
            border-color: #d69e2e !important;
            color: #744210 !important;
        }

        .theme-paper .el-button:hover {
            background: #edf2f7 !important;
            border-color: #b7791f !important;
        }

        .theme-dark .el-button--primary,
        .theme-gray .el-button--primary,
        .theme-forest .el-button--primary,
        .theme-ocean .el-button--primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            border-color: transparent !important;
        }

        /* 移动端响应式设计 - 修复键盘弹出问题 */
        @media (max-width: 768px) {
            /* 移动端头部固定定位，防止键盘弹出时上移 */
            .header {
                position: fixed !important;
                top: 0 !important; /* 直接贴顶，不使用safe-area */
                left: 0 !important;
                right: 0 !important;
                width: 100% !important;
                padding: 0px 16px !important; /* 移除所有内边距 */
                padding-top: env(safe-area-inset-top, 0) !important; /* 只使用safe-area作为顶部padding */
                background: #000000 !important; /* 移动端固定暗黑模式 */
                z-index: 9999 !important;
                box-sizing: border-box !important;
                min-height: calc(44px + env(safe-area-inset-top, 0)) !important; /* 设置最小高度 */
            }

            /* 为固定头部和工具栏预留空间 */
            body {
                padding-top: calc(44px + env(safe-area-inset-top, 0)) !important; /* 只为导航栏预留空间 */
                margin: 0 !important;
            }

            /* 确保Vue应用容器不影响布局 */
            #app {
                position: relative !important;
            }

            .content-type-selector {
                max-width: 100%;
                gap: 16px;
            }

            .editor-container {
                margin: 16px auto;
                border-radius: 12px;
                max-width: calc(100% - 32px);
                box-shadow: 0 6px 24px rgba(0,0,0,0.1), 0 2px 6px rgba(0,0,0,0.06);
            }

            .editor-container:hover {
                transform: none; /* 移动端禁用hover动画 */
            }

            /* 移动端编辑器内边距调整 - 全屏体验 */
            .vditor-ir .vditor-ir__main,
            .vditor-wysiwyg .vditor-wysiwyg__main,
            .vditor-sv .vditor-sv__main,
            .vditor-preview {
                padding: 20px 16px !important;
            }

            .content-type-selector {
                /* 保持水平布局，让所有按钮在一行 */
                flex-direction: row;
                align-items: center;
                justify-content: space-between;
                gap: 12px;
            }

            .type-buttons {
                display: flex;
                gap: 10px;
                flex-shrink: 0;
            }

            .action-buttons {
                display: flex;
                gap: 10px;
                flex-shrink: 0;
            }

            /* 移动端隐藏主题切换按钮，固定暗黑模式 */
            .theme-dropdown {
                display: none !important;
            }

            /* 优化移动端按钮样式 */
            .type-buttons .el-button {
                padding: 10px 16px !important;
                font-size: 14px !important;
                font-weight: 500 !important;
                border-radius: 20px !important;
            }

            /* 移动端隐藏所有类型选择按钮，默认Essays模式 */
            .type-buttons {
                display: none !important;
            }

            /* 移动端优化：重新布局，配置按钮在左边，发布按钮在右边 */
            .content-type-selector {
                justify-content: space-between !important;
                padding: 8px 16px !important; /* 添加垂直内边距 */
                align-items: center !important;
            }

            .action-buttons {
                order: -1; /* 将配置按钮移到左边 */
                display: flex !important;
                align-items: center !important;
            }

            /* 移动端发布按钮调整 */
            .mobile-publish-button {
                padding: 6px 10px !important;
                font-size: 12px !important;
                min-width: auto !important;
            }

            /* 移动端Vditor工具栏样式 */
            .vditor-toolbar {
                display: flex !important;
                background: #000000 !important;
                border-bottom: 1px solid #333 !important;
                padding: 8px !important;
                position: fixed !important; /* 改为fixed定位 */
                top: calc(44px + env(safe-area-inset-top, 0)) !important; /* 紧贴导航栏 */
                left: 0 !important;
                right: 0 !important;
                width: 100% !important;
                z-index: 1000 !important; /* 提高z-index */
                box-sizing: border-box !important;
            }

            /* 确保移动端上传按钮显示 */
            .vditor-toolbar .vditor-toolbar__item[data-type="upload"] {
                display: flex !important;
                visibility: visible !important;
                opacity: 1 !important;
                background: rgba(255,255,255,0.1) !important;
                border-radius: 6px !important;
                padding: 8px !important;
                margin: 0 4px !important;
            }

            .vditor-toolbar .vditor-toolbar__item[data-type="upload"] svg {
                fill: #fff !important;
                width: 20px !important;
                height: 20px !important;
            }

            /* 移动端工具栏按钮通用样式 */
            .vditor-toolbar .vditor-toolbar__item {
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                min-width: 36px !important;
                height: 36px !important;
                background: rgba(255,255,255,0.1) !important;
                border-radius: 6px !important;
                margin: 0 2px !important;
                cursor: pointer !important;
            }

            .vditor-toolbar .vditor-toolbar__item:hover {
                background: rgba(255,255,255,0.2) !important;
            }

            .vditor-toolbar .vditor-toolbar__item svg {
                fill: #fff !important;
                width: 18px !important;
                height: 18px !important;
            }

            /* 确保Vditor内容区域在工具栏下方 */
            .vditor-content,
            .vditor-ir,
            .vditor-wysiwyg,
            .vditor-sv {
                margin-top: 52px !important; /* 为工具栏预留空间 */
                padding-top: 0 !important;
            }

            /* Vditor编辑区域样式 */
            .vditor-ir .vditor-ir__main,
            .vditor-wysiwyg .vditor-wysiwyg__main,
            .vditor-sv .vditor-sv__main {
                padding-top: 20px !important;
                min-height: calc(100vh - 140px - env(safe-area-inset-top, 0)) !important;
            }

            /* 移动端全屏编辑器设计 - 修复键盘弹出时的滑动问题 */
            .editor-container {
                height: calc(100vh - 44px - env(safe-area-inset-top, 0)) !important; /* 调整高度 */
                height: calc(100dvh - 44px - env(safe-area-inset-top, 0)) !important; /* 移除额外高度 */
                margin: 0 !important;
                padding: 0 !important; /* 移除内边距 */
                border-radius: 0 !important;
                border: none !important;
                box-shadow: none !important;
                max-width: 100% !important;
                position: relative !important;
                top: calc(44px + env(safe-area-inset-top, 0)) !important; /* 紧贴导航栏 */
            }

            .editor-container:hover {
                transform: none !important;
                box-shadow: none !important;
            }



            /* 键盘弹出时的特殊处理 */
            body.keyboard-open .header {
                transform: translateY(0) !important;
                position: fixed !important;
                top: 0 !important;
            }

            body.keyboard-open .editor-container {
                height: calc(var(--vh, 1vh) * 100 - 70px) !important;
            }
        }

        /* 强制移动端头部固定定位 - 最高优先级 */
        @media (max-width: 768px) {
            html body #app .header,
            html body .header,
            .header {
                position: fixed !important;
                top: 0 !important; /* 直接贴顶 */
                left: 0 !important;
                right: 0 !important;
                width: 100% !important;
                z-index: 9999 !important;
                transform: none !important;
                padding-top: env(safe-area-inset-top, 0) !important; /* 只使用safe-area */
                min-height: calc(44px + env(safe-area-inset-top, 0)) !important;
            }
        }

            .github-config-button,
            .theme-toggle-button {
                width: 36px !important;
                height: 36px !important;
            }

            .publish-button {
                padding: 8px 12px !important;
            }
        }

        @media (max-width: 480px) {
            /* 超小屏幕头部调整 */
            .header {
                padding: 0px 12px !important;
                padding-top: env(safe-area-inset-top, 0) !important; /* 直接使用safe-area */
                min-height: calc(40px + env(safe-area-inset-top, 0)) !important;
            }

            .content-type-selector {
                gap: 8px;
                padding: 0 4px;
            }

            /* 超小屏幕编辑器调整 */
            .editor-container {
                height: calc(100vh - 40px - env(safe-area-inset-top, 0)) !important; /* fallback */
                height: calc(100dvh - 40px - env(safe-area-inset-top, 0)) !important;
                top: calc(40px + env(safe-area-inset-top, 0)) !important; /* 紧贴导航栏 */
            }

            /* 超小屏幕Vditor工具栏调整 */
            .vditor-toolbar {
                top: calc(40px + env(safe-area-inset-top, 0)) !important;
            }

            /* 超小屏幕Vditor内容区域调整 */
            .vditor-content,
            .vditor-ir,
            .vditor-wysiwyg,
            .vditor-sv {
                margin-top: 48px !important; /* 为较小的工具栏预留空间 */
            }

            /* 调整body的padding */
            body {
                padding-top: calc(48px + env(safe-area-inset-top, 0)) !important;
            }

            /* 超小屏幕编辑器内边距调整 - 最大化写作空间 */
            .vditor-ir .vditor-ir__main,
            .vditor-wysiwyg .vditor-wysiwyg__main,
            .vditor-sv .vditor-sv__main,
            .vditor-preview {
                padding: 16px 12px !important;
            }

            /* 超小屏幕按钮优化 */
            .type-buttons .el-button {
                padding: 8px 14px !important;
                font-size: 13px !important;
            }

            /* 超小屏幕Essays按钮优化 */
            .type-buttons .el-button[data-type="essay"] {
                max-width: 120px;
            }

            .github-config-button,
            .theme-toggle-button {
                width: 36px !important;
                height: 36px !important;
                border-radius: 18px !important;
            }

            .publish-button {
                padding: 8px 12px !important;
                font-size: 13px !important;
            }

            /* 超小屏幕移动端发布按钮 */
            .mobile-publish-button {
                padding: 4px 8px !important;
                font-size: 11px !important;
                min-width: auto !important;
            }

            .type-buttons,
            .action-buttons {
                gap: 8px;
            }
        }

        /* 移动端弹窗优化 */
        @media (max-width: 768px) {
            .el-dialog {
                width: 90% !important;
                max-width: 400px !important;
                margin: 0 auto !important;
                top: 15vh !important;
                transform: none !important;
            }

            .el-dialog__header {
                padding: 15px 20px 10px !important;
            }

            .el-dialog__body {
                padding: 10px 20px 20px !important;
                max-height: 60vh !important;
                overflow-y: auto !important;
            }

            .el-dialog__footer {
                padding: 10px 20px 15px !important;
            }
        }

        @media (max-width: 480px) {
            .el-dialog {
                width: 95% !important;
                max-width: 350px !important;
                top: 10vh !important;
            }

            .el-dialog__header {
                padding: 12px 15px 8px !important;
            }

            .el-dialog__body {
                padding: 8px 15px 15px !important;
                max-height: 65vh !important;
                font-size: 14px !important;
            }

            .el-dialog__footer {
                padding: 8px 15px 12px !important;
            }

            /* 移动端表单元素优化 */
            .el-input__inner {
                font-size: 16px !important; /* 防止iOS缩放 */
            }

            .el-button {
                padding: 8px 15px !important;
                font-size: 14px !important;
            }
        }

        /* 图片拖拽样式 */
        .drag-over {
            border: 2px dashed #002FA7 !important;
            background-color: rgba(0, 47, 167, 0.1) !important;
            position: relative;
        }

        .drag-over::after {
            content: '拖拽图片到此处上传';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 47, 167, 0.9);
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            font-size: 16px;
            z-index: 1000;
            pointer-events: none;
        }

        /* 图片上传进度样式 */
        .upload-progress {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            border-radius: 5px;
            z-index: 2000;
            min-width: 300px;
        }

        .upload-progress .progress-item {
            margin-bottom: 10px;
        }

        .upload-progress .progress-item:last-child {
            margin-bottom: 0;
        }

        /* PWA配置对话框样式 */
        .pwa-config-dialog .el-message-box {
            width: 90% !important;
            max-width: 400px !important;
        }

        .pwa-config-dialog .el-message-box__message {
            font-size: 14px !important;
            line-height: 1.6 !important;
            color: #606266 !important;
        }

        .pwa-config-dialog .el-message-box__btns {
            padding: 15px 20px 20px !important;
        }

        .pwa-config-dialog .el-button {
            font-size: 14px !important;
            padding: 8px 16px !important;
        }

        /* 快速配置按钮样式 */
        .quick-config-button {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4) !important;
            border: none !important;
            animation: quickConfigPulse 2s infinite;
        }

        .quick-config-button:hover {
            background: linear-gradient(45deg, #FF5252, #26C6DA) !important;
            transform: scale(1.05);
        }

        @keyframes quickConfigPulse {
            0%, 100% {
                box-shadow: 0 0 5px rgba(255, 107, 107, 0.5);
            }
            50% {
                box-shadow: 0 0 15px rgba(78, 205, 196, 0.7);
            }
        }

        /* 主题切换按钮样式 */
        .theme-toggle-button {
            width: 44px;
            height: 44px;
            border-radius: 12px;
            border: none;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            background: rgba(255,255,255,0.15);
            color: white;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .theme-toggle-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.2);
            background: rgba(255,255,255,0.25);
        }

        .theme-toggle-button .el-icon-brush {
            font-size: 18px;
        }

        /* 主题选择下拉菜单样式 */
        .theme-dropdown {
            position: relative;
            display: inline-block;
        }

        .theme-menu {
            position: absolute;
            top: 50px;
            right: 0;
            background: #161b22;
            border: 1px solid #30363d;
            border-radius: 8px;
            box-shadow: 0 8px 24px rgba(0,0,0,0.5);
            z-index: 1000;
            min-width: 150px;
            overflow: hidden;
        }

        .theme-menu-item {
            padding: 12px 16px;
            cursor: pointer;
            transition: background 0.2s ease;
            color: #c9d1d9;
            border-bottom: 1px solid #30363d;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .theme-menu-item:last-child {
            border-bottom: none;
        }

        .theme-menu-item:hover {
            background: #21262d;
        }

        .theme-menu-item.active {
            background: #0969da;
            color: white;
        }

        .theme-color-preview {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 2px solid rgba(255,255,255,0.3);
        }

        /* 主题菜单在不同主题下的样式 */
        .theme-paper .theme-menu {
            background: #fffbeb;
            border-color: #d69e2e;
        }

        .theme-paper .theme-menu-item {
            color: #744210;
            border-bottom-color: #d69e2e;
        }

        .theme-paper .theme-menu-item:hover {
            background: #fef7e0;
        }

        .theme-paper .theme-menu-item.active {
            background: #d69e2e;
            color: white;
        }


    </style>
</head>
<body>
    <div id="app">
        <div class="header">
            <div class="content-type-selector">
                <div class="type-buttons">
                    <el-button
                        v-for="(label, type) in contentTypeLabels"
                        :key="type"
                        :type="currentType === type ? 'primary' : 'default'"
                        @click="selectType(type)"
                        size="small"
                        :data-type="type">
                        <i :class="getTypeIcon(type)"></i>
                        {{ label }}
                    </el-button>
                </div>
                <div class="action-buttons">
                    <button class="github-config-button" @click="configureGitHub" :title="isGitHubConfigured ? 'GitHub已配置' : '配置GitHub'">
                        <i class="el-icon-setting"></i>
                    </button>
                    <button class="github-config-button" @click="configureImageService" :title="isImageServiceConfigured ? '图床已配置' : '配置图床'" style="margin-left: 5px;">
                        <img src="./img/icons/pic.png" alt="图床配置" style="width: 16px; height: 16px;">
                    </button>
                    <button v-if="isPWAMode && !isImageServiceConfigured" class="github-config-button quick-config-button" @click="showQuickConfig" title="PWA快速配置" style="margin-left: 5px;">
                        <i class="el-icon-magic-stick"></i>
                    </button>
                    <!-- 移动端发布按钮 -->
                    <el-button v-if="isMobileDevice()" @click="publish" size="small" type="success" icon="el-icon-upload" :disabled="!isGitHubConfigured" class="publish-button mobile-publish-button" style="margin-left: 8px;">
                    </el-button>
                    <div class="theme-dropdown">
                        <button class="theme-toggle-button" @click="toggleThemeMenu" title="切换主题">
                            <i class="el-icon-brush"></i>
                        </button>
                        <div v-if="showThemeMenu" class="theme-menu" @click.stop>
                            <div
                                v-for="theme in themes"
                                :key="theme.id"
                                class="theme-menu-item"
                                :class="{ active: currentTheme === theme.id }"
                                @click="selectTheme(theme.id)">
                                <div class="theme-color-preview" :style="{ backgroundColor: theme.color }"></div>
                                {{ theme.name }}
                            </div>
                        </div>
                    </div>
                    <el-button v-if="!isMobileDevice()" @click="publish" size="medium" type="success" icon="el-icon-upload" :disabled="!isGitHubConfigured" class="publish-button desktop-publish-button">
                    </el-button>
                </div>
            </div>
        </div>

        <div class="editor-container">
            <!-- 元数据编辑器将通过JS移动到Vditor工具栏下方 -->
            <div id="metadata-editor-wrapper" style="display: none;">
                <div class="metadata-editor"
                     v-if="currentType !== 'general' && currentType !== 'gallery' && currentType !== 'essay' && hasVisibleFields">
                    <el-input v-if="metadataFields[currentType].some(f => f.key === 'title')" v-model="metadata.title" placeholder="Title" size="small"></el-input>
                    <el-input v-if="metadataFields[currentType].some(f => f.key === 'categories')" v-model="metadata.categories" placeholder="Categories (comma-separated)" size="small"></el-input>
                    <el-input v-if="metadataFields[currentType].some(f => f.key === 'description')" v-model="metadata.description" placeholder="Description" size="small"></el-input>
                    <el-date-picker v-if="metadataFields[currentType].some(f => f.key === 'date' && f.type === 'date')" v-model="metadata.date" type="date" placeholder="拍摄日期" size="small" format="yyyy-MM-dd" value-format="yyyy-MM-dd"></el-date-picker>
                </div>
            </div>

            <div id="vditor"></div>
        </div>



        <github-config
            :visible="githubConfigVisible"
            @close="githubConfigVisible = false"
            @save="handleGitHubConfigSave">
        </github-config>

        <image-service-config
            :visible="imageConfigVisible"
            @close="imageConfigVisible = false"
            @save="handleImageServiceConfigSave">
        </image-service-config>

        <!-- 上传进度提示 -->
        <el-dialog
            title="图片上传中"
            :visible.sync="uploadProgress.visible"
            width="400px"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :show-close="false">
            <div style="text-align: center;">
                <p>正在上传第 {{ uploadProgress.current }} / {{ uploadProgress.total }} 张图片</p>
                <p>{{ uploadProgress.fileName }}</p>
                <p>{{ getStageText(uploadProgress.stage) }}</p>
                <div v-if="uploadProgress.stage === 'compressing' && uploadProgress.currentSize > 0" style="font-size: 12px; color: #666; margin: 5px 0;">
                    <p>压缩质量: {{ Math.round(uploadProgress.quality * 100) }}%</p>
                    <p>当前大小: {{ formatFileSize(uploadProgress.currentSize) }} / 目标: {{ formatFileSize(uploadProgress.targetSize) }}</p>
                </div>
                <el-progress :percentage="uploadProgress.progress" :show-text="false"></el-progress>
            </div>
        </el-dialog>

        <!-- 移动端错误显示对话框 -->
        <el-dialog
            title="错误详情"
            :visible.sync="mobileErrorDialog.visible"
            width="90%"
            :close-on-click-modal="false">
            <div style="font-family: monospace; font-size: 12px; white-space: pre-wrap; max-height: 400px; overflow-y: auto; background: #f5f5f5; padding: 10px; border-radius: 4px;">
                {{ mobileErrorDialog.content }}
            </div>
            <div slot="footer" class="dialog-footer">
                <el-button @click="copyErrorToClipboard">复制错误信息</el-button>
                <el-button type="primary" @click="mobileErrorDialog.visible = false">关闭</el-button>
            </div>
        </el-dialog>
    </div>

    <script>
        // 等待页面完全加载后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化GitHub服务
            window.githubService = new GitHubService();

        // 配置常量
        const contentTypes = {
            GENERAL: 'general',
            BLOG: 'blog',
            ESSAY: 'essay',
            GALLERY: 'gallery'
        };

        const contentTypeLabels = {
            [contentTypes.BLOG]: 'Blogs',
            [contentTypes.ESSAY]: 'Essays',
            [contentTypes.GALLERY]: 'Gallery'
        };

        const metadataFields = {
            [contentTypes.BLOG]: [
                { key: 'title', label: '标题', type: 'text', required: true },
                { key: 'categories', label: '分类', type: 'tags' },
                { key: 'date', label: '发布日期', type: 'datetime' }
            ],
            [contentTypes.ESSAY]: [
                { key: 'pubDate', label: '发布日期', type: 'datetime' }
            ],
            [contentTypes.GALLERY]: [
                // Gallery模式不显示任何元数据字段，date在后台自动添加
            ]
        };

        // 模板生成函数
        function generateContentWithMetadata(contentType, metadata, content) {
            if (contentType === contentTypes.GENERAL) {
                return content;
            }

            const frontmatter = Object.keys(metadata)
                .filter(key => metadata[key] !== undefined && metadata[key] !== '')
                .map(key => {
                    let value = metadata[key];
                    if (key === 'categories' && typeof value === 'string') {
                        value = value.split(',').map(cat => cat.trim()).filter(cat => cat);
                    }
                    if (typeof value === 'string' && value.includes(':')) {
                        value = `"${value}"`;
                    }
                    return `${key}: ${Array.isArray(value) ? JSON.stringify(value) : value}`;
                })
                .join('\n');

            return `---\n${frontmatter}\n---\n\n${content}`;
        }

        // GitHub配置组件
        Vue.component('github-config', {
            props: ['visible'],
            data() {
                return {
                    config: {
                        token: '',
                        owner: '',
                        repo: ''
                    },
                    testing: false,
                    testResult: null
                };
            },
            watch: {
                visible(newVal) {
                    if (newVal) {
                        this.loadConfig();
                    }
                }
            },
            methods: {
                loadConfig() {
                    const saved = localStorage.getItem('github-config');
                    if (saved) {
                        this.config = JSON.parse(saved);
                    }
                },
                async testConnection() {
                    if (!this.config.token || !this.config.owner || !this.config.repo) {
                        this.$message.warning('请填写完整的配置信息');
                        return;
                    }

                    this.testing = true;
                    this.testResult = null;

                    try {
                        // 临时设置配置进行测试
                        const tempService = new GitHubService();
                        tempService.setConfig(this.config);

                        const result = await tempService.testConnection();

                        if (result.success) {
                            this.testResult = {
                                success: true,
                                message: `连接成功！用户: ${result.user}，仓库: ${result.repo}，权限: ${result.permissions}`
                            };
                        } else {
                            this.testResult = {
                                success: false,
                                message: result.error
                            };
                        }
                    } catch (error) {
                        this.testResult = {
                            success: false,
                            message: `连接失败: ${error.message}`
                        };
                    } finally {
                        this.testing = false;
                    }
                },
                save() {
                    if (!this.config.token || !this.config.owner || !this.config.repo) {
                        this.$message.warning('请填写完整的配置信息');
                        return;
                    }

                    localStorage.setItem('github-config', JSON.stringify(this.config));
                    this.$emit('save', this.config);
                    this.close();
                },
                close() {
                    this.$emit('close');
                }
            },
            template: `
                <el-dialog
                    title="GitHub Configuration"
                    :visible.sync="visible"
                    width="550px"
                    @close="close"
                    custom-class="github-config-dialog">
                    <el-form :model="config" label-width="140px">
                        <el-form-item label="Personal Token">
                            <el-input
                                v-model="config.token"
                                type="password"
                                placeholder="Enter your GitHub Personal Access Token"
                                show-password>
                            </el-input>
                            <div class="token-instructions">
                                <strong>How to get a token:</strong><br>
                                1. GitHub → Settings → Developer settings<br>
                                2. Personal access tokens → Tokens (classic)<br>
                                3. Generate new token (classic)<br>
                                4. Check the <strong>repo</strong> scope → Generate token<br>
                                5. Copy the token and paste it here.
                            </div>
                        </el-form-item>

                        <el-form-item label="Repository Owner">
                            <el-input
                                v-model="config.owner"
                                placeholder="Your GitHub username or organization">
                            </el-input>
                        </el-form-item>

                        <el-form-item label="Repository Name">
                            <el-input
                                v-model="config.repo"
                                placeholder="The name of the repository">
                            </el-input>
                        </el-form-item>
                    </el-form>

                    <div v-if="testResult" :class="['test-result', testResult.success ? 'success' : 'error']">
                        {{ testResult.message }}
                    </div>

                    <div slot="footer" class="dialog-footer">
                        <el-button @click="testConnection" :loading="testing">Test Connection</el-button>
                        <el-button @click="close">Cancel</el-button>
                        <el-button type="primary" @click="save" class="save-button">Save Configuration</el-button>
                    </div>
                </el-dialog>
            `
        });

        // 图片服务配置组件
        Vue.component('image-service-config', {
            props: ['visible'],
            data() {
                return {
                    config: {
                        token: '',
                        owner: '',
                        repo: '',
                        branch: 'master',  // 改为默认使用master分支
                        imageDir: 'images'
                    },
                    linkRules: {
                        'GitHub': 'GitHub原始链接',
                        'jsDelivr': 'jsDelivr CDN',
                        'Statically': 'Statically CDN',
                        'ChinaJsDelivr': '中国jsDelivr CDN'
                    },
                    selectedLinkRule: 'jsDelivr',
                    testing: false,
                    testResult: null
                };
            },
            watch: {
                visible(newVal) {
                    if (newVal) {
                        this.loadConfig();
                    }
                }
            },
            methods: {
                loadConfig() {
                    const saved = localStorage.getItem('image-service-config');
                    if (saved) {
                        const savedConfig = JSON.parse(saved);
                        // 强制修正分支配置，如果是main则改为master
                        if (savedConfig.branch === 'main') {
                            savedConfig.branch = 'master';
                            localStorage.setItem('image-service-config', JSON.stringify(savedConfig));
                        }
                        this.config = { ...this.config, ...savedConfig };
                    }

                    const savedRule = localStorage.getItem('image-service-link-rule');
                    if (savedRule) {
                        this.selectedLinkRule = savedRule;
                    }
                },
                async testConnection() {
                    if (!this.config.token || !this.config.owner || !this.config.repo) {
                        this.$message.warning('请填写完整的配置信息');
                        return;
                    }

                    this.testing = true;
                    this.testResult = null;

                    try {
                        // 临时设置配置进行测试
                        const tempService = new ImageService();
                        tempService.setConfig(this.config);

                        const result = await tempService.testConnection();

                        if (result.success) {
                            this.testResult = {
                                success: true,
                                message: `连接成功！用户: ${result.user}，仓库: ${result.repo}，权限: ${result.permissions}`
                            };
                        } else {
                            this.testResult = {
                                success: false,
                                message: result.error
                            };
                        }
                    } catch (error) {
                        this.testResult = {
                            success: false,
                            message: `连接失败: ${error.message}`
                        };
                    } finally {
                        this.testing = false;
                    }
                },
                save() {
                    if (!this.config.token || !this.config.owner || !this.config.repo) {
                        this.$message.warning('请填写完整的配置信息');
                        return;
                    }

                    // 保存配置
                    const configToSave = { ...this.config };
                    localStorage.setItem('image-service-config', JSON.stringify(configToSave));
                    localStorage.setItem('image-service-link-rule', this.selectedLinkRule);

                    // 设置CDN规则
                    if (window.imageService) {
                        window.imageService.setLinkRule(this.selectedLinkRule);
                    }

                    this.$emit('save', configToSave);
                    this.close();
                },
                close() {
                    this.$emit('close');
                }
            },
            template: `
                <el-dialog
                    title="图床配置"
                    :visible.sync="visible"
                    width="600px"
                    @close="close"
                    custom-class="github-config-dialog">
                    <el-form :model="config" label-width="120px">
                        <el-form-item label="GitHub Token">
                            <el-input
                                v-model="config.token"
                                type="password"
                                placeholder="输入GitHub Personal Access Token"
                                show-password>
                            </el-input>
                            <div class="token-instructions">
                                <strong>注意：</strong>建议使用专门的图床仓库，与博客仓库分开管理
                            </div>
                        </el-form-item>

                        <el-form-item label="仓库所有者">
                            <el-input
                                v-model="config.owner"
                                placeholder="GitHub用户名或组织名">
                            </el-input>
                        </el-form-item>

                        <el-form-item label="图床仓库名">
                            <el-input
                                v-model="config.repo"
                                placeholder="专用图床仓库名称">
                            </el-input>
                        </el-form-item>

                        <el-form-item label="分支名称">
                            <el-input
                                v-model="config.branch"
                                placeholder="默认为master">
                            </el-input>
                        </el-form-item>

                        <el-form-item label="图片目录">
                            <el-input
                                v-model="config.imageDir"
                                placeholder="图片存储目录，默认为images">
                            </el-input>
                        </el-form-item>

                        <el-form-item label="CDN服务">
                            <el-select v-model="selectedLinkRule" placeholder="选择CDN服务">
                                <el-option
                                    v-for="(label, key) in linkRules"
                                    :key="key"
                                    :label="label"
                                    :value="key">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-form>

                    <div v-if="testResult" :class="['test-result', testResult.success ? 'success' : 'error']">
                        {{ testResult.message }}
                    </div>

                    <div slot="footer" class="dialog-footer">
                        <el-button @click="testConnection" :loading="testing">测试连接</el-button>
                        <el-button @click="close">取消</el-button>
                        <el-button type="primary" @click="save" class="save-button">保存配置</el-button>
                    </div>
                </el-dialog>
            `
        });

        // 主Vue实例
        new Vue({
            el: '#app',
            data() {
                return {
                    vditor: null,
                    currentType: contentTypes.ESSAY, // 默认为Essays模式
                    metadata: {},
                    githubConfigVisible: false,
                    contentTypeLabels,
                    isGitHubConfigured: false,
                    isDarkMode: true,
                    metadataFields,
                    bodyContent: '',
                    hasUserInteracted: false,
                    // 图片上传相关
                    imageConfigVisible: false,
                    isImageServiceConfigured: false,
                    uploadingImages: false,
                    uploadProgress: {
                        visible: false,
                        current: 0,
                        total: 0,
                        fileName: '',
                        stage: '',
                        progress: 0,
                        currentSize: 0,
                        targetSize: 0,
                        quality: 0
                    },
                    // 移动端错误显示
                    mobileErrorDialog: {
                        visible: false,
                        content: ''
                    },
                    // PWA模式检测
                    isPWAMode: false,
                    // Gallery模式相关
                    galleryPreview: {
                        url: null,
                        fileName: '',
                        size: 0,
                        file: null
                    },
                    // 主题相关
                    currentTheme: 'dark',
                    showThemeMenu: false,
                    themes: [
                        { id: 'dark', name: '暗夜模式', color: '#000000' },
                        { id: 'gray', name: '深灰模式', color: '#2d3748' },
                        { id: 'paper', name: '纸黄模式', color: '#fef7e0' },
                        { id: 'forest', name: '森林模式', color: '#1a2e1a' },
                        { id: 'ocean', name: '海洋模式', color: '#1a365d' }
                    ]
                };
            },

            computed: {
                hasVisibleFields() {
                    if (!this.currentType || this.currentType === 'general' || this.currentType === 'gallery') {
                        return false;
                    }
                    const fields = this.metadataFields[this.currentType];
                    return fields && fields.length > 0;
                }
            },

            mounted() {
                this.initTheme();
                this.setupMobileDefaults();
                this.initVditor();
                this.checkGitHubConfig();
                this.checkImageServiceConfig();
                this.checkForEditFile();
                this.selectType(this.currentType);
                this.detectPWAMode();
                this.checkPWAConfigMissing();
            },

            methods: {
                // 检测是否为移动设备
                isMobileDevice() {
                    const userAgent = navigator.userAgent.toLowerCase();
                    const mobileKeywords = ['mobile', 'android', 'iphone', 'ipad', 'ipod', 'blackberry', 'windows phone'];
                    return mobileKeywords.some(keyword => userAgent.includes(keyword)) ||
                           window.innerWidth <= 768;
                },

                // 为移动设备设置默认配置
                setupMobileDefaults() {
                    if (this.isMobileDevice()) {
                        // 设置为Essays模式
                        this.currentType = contentTypes.ESSAY;
                        // 移动端固定使用暗黑模式
                        this.currentTheme = 'dark';
                        this.applyTheme('dark');
                        localStorage.setItem('selected-theme', 'dark');

                        // 修复移动端键盘弹出问题
                        this.setupMobileKeyboardFix();
                    }
                },

                // 修复移动端键盘弹出时的布局问题
                setupMobileKeyboardFix() {
                    // 获取状态栏高度
                    const safeAreaTop = this.getSafeAreaTop();

                    // 强制设置移动端头部为固定定位
                    const header = document.querySelector('.header');
                    if (header) {
                        header.style.position = 'fixed';
                        header.style.top = '0'; /* 直接贴顶 */
                        header.style.left = '0';
                        header.style.right = '0';
                        header.style.width = '100%';
                        header.style.zIndex = '9999';
                        header.style.boxSizing = 'border-box';
                        header.style.paddingTop = safeAreaTop + 'px'; /* 只使用safe-area */
                        header.style.minHeight = (44 + safeAreaTop) + 'px'; /* 设置最小高度 */
                    }

                    // 强制设置body的padding-top
                    document.body.style.paddingTop = (44 + safeAreaTop) + 'px'; /* 减少预留空间 */
                    document.body.style.margin = '0';

                    // 监听视口变化
                    const initialViewportHeight = window.innerHeight;

                    window.addEventListener('resize', () => {
                        const currentHeight = window.innerHeight;
                        const heightDiff = initialViewportHeight - currentHeight;

                        // 如果高度减少超过150px，认为是键盘弹出
                        if (heightDiff > 150) {
                            document.body.classList.add('keyboard-open');
                            // 键盘弹出时确保头部仍然固定
                            if (header) {
                                header.style.position = 'fixed';
                                header.style.top = '0'; /* 直接贴顶 */
                                header.style.transform = 'translateY(0)';
                            }
                        } else {
                            document.body.classList.remove('keyboard-open');
                        }
                    });

                    // 防止iOS Safari地址栏影响
                    const setViewportHeight = () => {
                        const vh = window.innerHeight * 0.01;
                        document.documentElement.style.setProperty('--vh', `${vh}px`);
                    };

                    setViewportHeight();
                    window.addEventListener('resize', setViewportHeight);

                    // 防止滚动时头部移动
                    window.addEventListener('scroll', () => {
                        if (header && this.isMobileDevice()) {
                            header.style.position = 'fixed';
                            header.style.top = '0'; /* 直接贴顶 */
                        }
                    });
                },

                // 获取安全区域顶部高度（状态栏高度）
                getSafeAreaTop() {
                    // 尝试使用CSS env()函数获取
                    const testElement = document.createElement('div');
                    testElement.style.paddingTop = 'env(safe-area-inset-top)';
                    document.body.appendChild(testElement);
                    const computedPadding = window.getComputedStyle(testElement).paddingTop;
                    document.body.removeChild(testElement);

                    if (computedPadding && computedPadding !== '0px') {
                        return parseInt(computedPadding);
                    }

                    // 回退方案：根据设备类型估算
                    const userAgent = navigator.userAgent;
                    if (/iPhone/.test(userAgent)) {
                        // iPhone X及以上有刘海屏
                        if (window.screen.height >= 812) {
                            return 44; // iPhone X/XS/11 Pro等
                        } else {
                            return 20; // 传统iPhone
                        }
                    } else if (/iPad/.test(userAgent)) {
                        return 20;
                    } else if (/Android/.test(userAgent)) {
                        return 24; // 大多数Android设备
                    }

                    return 0; // 默认值
                },

                initTheme() {
                    // 从localStorage加载保存的主题，默认为暗夜模式
                    const savedTheme = localStorage.getItem('selected-theme') || 'dark';
                    this.currentTheme = savedTheme;
                    this.applyTheme(savedTheme);
                },

                // 应用主题
                applyTheme(themeId) {
                    // 移除所有主题类
                    document.body.classList.remove('theme-dark', 'theme-gray', 'theme-paper', 'theme-forest', 'theme-ocean');
                    // 添加新主题类
                    document.body.classList.add(`theme-${themeId}`);

                    // 更新meta标签颜色
                    const themeColors = {
                        dark: '#000000',
                        gray: '#2d3748',
                        paper: '#fef7e0',
                        forest: '#1a2e1a',
                        ocean: '#1a365d'
                    };

                    const metaThemeColor = document.querySelector('meta[name="theme-color"]');
                    if (metaThemeColor) {
                        metaThemeColor.setAttribute('content', themeColors[themeId]);
                    }

                    // 保存到localStorage
                    localStorage.setItem('selected-theme', themeId);
                },

                // 切换主题菜单显示
                toggleThemeMenu() {
                    this.showThemeMenu = !this.showThemeMenu;

                    // 点击其他地方关闭菜单
                    if (this.showThemeMenu) {
                        const closeMenu = (e) => {
                            if (!e.target.closest('.theme-dropdown')) {
                                this.showThemeMenu = false;
                                document.removeEventListener('click', closeMenu);
                            }
                        };
                        setTimeout(() => {
                            document.addEventListener('click', closeMenu);
                        }, 0);
                    }
                },

                // 选择主题
                selectTheme(themeId) {
                    this.currentTheme = themeId;
                    this.applyTheme(themeId);
                    this.showThemeMenu = false;

                    // 更新Vditor主题
                    if (this.vditor) {
                        const vditorTheme = themeId === 'paper' ? 'classic' : 'dark';
                        this.vditor.setTheme(vditorTheme, vditorTheme, vditorTheme);
                    }
                },

                initVditor() {
                    const editorMode = 'ir';
                    const vditorTheme = this.currentTheme === 'paper' ? 'classic' : 'dark';

                    // 根据设备类型设置不同的工具栏
                    const isMobile = this.isMobileDevice();
                    const toolbar = isMobile ? [
                        // 移动端简化工具栏：保留常用功能和图片上传
                        'bold', 'italic', '|', 'list', 'ordered-list', '|', 'upload', '|', 'undo', 'redo'
                    ] : [
                        // 桌面端完整工具栏
                        'emoji', 'headings', 'bold', 'italic', 'strike', 'link', '|',
                        'list', 'ordered-list', 'check', 'outdent', 'indent', '|',
                        'quote', 'line', 'code', 'inline-code', 'insert-before', 'insert-after', '|',
                        'upload', 'table', '|',
                        'undo', 'redo', '|',
                        'edit-mode', 'content-theme', 'code-theme', '|',
                        'outline', 'preview', 'fullscreen'
                    ];

                    this.vditor = new Vditor('vditor', {
                        height: '100%',
                        mode: editorMode,
                        theme: vditorTheme,
                        preview: {
                            theme: { current: vditorTheme },
                            actions: []
                        },
                        toolbar: toolbar,
                        upload: {
                            accept: 'image/*',
                            multiple: true,
                            fieldName: 'file[]',
                            filename: (name) => name.replace(/[^(a-zA-Z0-9\u4e00-\u9fa5\.)]/g, ''),
                            handler: (files) => {
                                return this.handleImageUpload(files);
                            }
                        },
                        cache: { enable: false },
                        after: function() {

                            // 将元数据编辑器移动到工具栏下方
                            const vditorElement = document.getElementById('vditor');
                            const toolbar = vditorElement.querySelector('.vditor-toolbar');
                            const metadataEditor = document.getElementById('metadata-editor-wrapper');
                            if (toolbar && metadataEditor) {
                                toolbar.insertAdjacentElement('afterend', metadataEditor);
                                metadataEditor.style.display = 'block';
                            }

                            // 移动端特殊处理：确保工具栏在正确位置
                            if (this.isMobileDevice() && toolbar) {
                                // 确保工具栏在移动端的正确定位
                                toolbar.style.position = 'fixed';
                                toolbar.style.top = 'calc(44px + env(safe-area-inset-top, 0))';
                                toolbar.style.left = '0';
                                toolbar.style.right = '0';
                                toolbar.style.width = '100%';
                                toolbar.style.zIndex = '1000';

                                // 确保编辑区域在工具栏下方
                                const contentArea = vditorElement.querySelector('.vditor-content') ||
                                                  vditorElement.querySelector('.vditor-ir') ||
                                                  vditorElement.querySelector('.vditor-wysiwyg') ||
                                                  vditorElement.querySelector('.vditor-sv');
                                if (contentArea) {
                                    contentArea.style.marginTop = '52px';
                                    contentArea.style.paddingTop = '0';
                                }
                            }

                            // 绑定 input 事件，将编辑器内容同步到 bodyContent
                            this.vditor.vditor.element.addEventListener('input', () => {
                                this.bodyContent = this.vditor.getValue();
                            });

                            // 设置拖拽和粘贴支持
                            this.setupImageDragAndPaste();

                            // 编辑器初始化完成后，触发自动聚焦
                            this.$nextTick(() => {
                                if (this.isMobileDevice()) {
                                    this.setupMobileAutoFocus();
                                } else {
                                    this.setupDesktopAutoFocus();
                                }
                            });
                        }.bind(this)
                    });
                },

                checkGitHubConfig() {
                    const config = localStorage.getItem('github-config');
                    this.isGitHubConfigured = !!config;
                    if (config && window.githubService) {
                        const parsedConfig = JSON.parse(config);
                        window.githubService.setConfig(parsedConfig);
                    }
                },

                checkForEditFile() {
                    const urlParams = new URLSearchParams(window.location.search);
                    const editFile = urlParams.get('edit');
                    if (editFile) {
                        this.loadFileForEdit(editFile);
                    }
                },

                async loadFileForEdit(filePath) {
                    if (!this.isGitHubConfigured) {
                        this.$message.warning('请先配置GitHub');
                        return;
                    }
                    try {
                        this.$message({ message: '正在加载文件...', type: 'info' });
                        const fileData = await window.githubService.getFile(filePath);
                        if (fileData) {
                            const { metadata, content } = this.parseContentWithFrontmatter(fileData.content);
                            
                            if (filePath.includes('/posts/')) {
                                this.currentType = contentTypes.BLOG;
                            } else if (filePath.includes('/essays/')) {
                                this.currentType = contentTypes.ESSAY;
                            }
                            
                            this.metadata = metadata;
                            this.bodyContent = content;
                            this.vditor.setValue(content);
                            this.$message.success('文件加载成功');
                        }
                    } catch (error) {
                        this.$message.error(`加载文件失败: ${error.message}`);
                    }
                },

                getTypeIcon(type) {
                    const icons = {
                        [contentTypes.BLOG]: 'el-icon-document',
                        [contentTypes.ESSAY]: 'el-icon-edit-outline',
                        [contentTypes.GALLERY]: 'el-icon-picture'
                    };
                    return icons[type] || 'el-icon-document';
                },

                selectType(type) {
                    this.currentType = type;
                    this.resetMetadata(type);
                    this.bodyContent = '';
                    this.vditor.setValue('');

                    // Gallery模式重置预览
                    if (type === contentTypes.GALLERY) {
                        this.resetGalleryPreview();
                    }
                },

                resetMetadata(type) {
                    const now = new Date();
                    let newMeta = {};

                    if (type === contentTypes.BLOG) {
                        const dateStr = now.toISOString().split('T')[0];
                        newMeta = {
                            title: '',
                            categories: 'Daily',
                            pubDate: dateStr
                        };
                    } else if (type === contentTypes.ESSAY) {
                        const dateTimeStr = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
                        newMeta = {
                            pubDate: dateTimeStr
                        };
                    } else if (type === contentTypes.GALLERY) {
                        const dateStr = now.toISOString().split('T')[0];
                        newMeta = {
                            date: dateStr
                        };
                    }
                    this.metadata = newMeta;
                },

                setCursorToPosition(position) {
                    try {
                        // 方法1: 尝试使用Vditor的API
                        if (this.vditor.setCursorPosition) {
                            this.vditor.setCursorPosition(position);
                            return;
                        }
                    } catch (e) {
                        // Vditor API method failed, trying direct DOM manipulation
                    }

                    // 方法2: 直接操作DOM元素
                    const editor = this.vditor.vditor.element.querySelector('.vditor-textarea') ||
                                 this.vditor.vditor.element.querySelector('textarea') ||
                                 this.vditor.vditor.element.querySelector('.vditor-ir');

                    if (editor) {
                        if (editor.setSelectionRange) {
                            editor.setSelectionRange(position, position);
                            editor.focus();
                        } else if (editor.createTextRange) {
                            // IE兼容
                            const range = editor.createTextRange();
                            range.move('character', position);
                            range.select();
                        }
                    }
                },

                parseContentWithFrontmatter(content) {
                    // 检查是否有frontmatter
                    if (!content.startsWith('---')) {
                        return { metadata: {}, content: content };
                    }

                    const lines = content.split('\n');
                    let frontmatterEnd = -1;

                    // 找到frontmatter结束位置
                    for (let i = 1; i < lines.length; i++) {
                        if (lines[i].trim() === '---') {
                            frontmatterEnd = i;
                            break;
                        }
                    }

                    if (frontmatterEnd === -1) {
                        return { metadata: {}, content: content };
                    }

                    // 提取frontmatter
                    const frontmatterLines = lines.slice(1, frontmatterEnd);
                    const bodyContent = lines.slice(frontmatterEnd + 1).join('\n');

                    // 解析frontmatter
                    const metadata = {};
                    frontmatterLines.forEach(line => {
                        const colonIndex = line.indexOf(':');
                        if (colonIndex > 0) {
                            const key = line.substring(0, colonIndex).trim();
                            let value = line.substring(colonIndex + 1).trim();

                            // 处理空值
                            if (!value || value.trim() === '') {
                                value = '';
                            } else {
                                // 移除引号
                                if ((value.startsWith('"') && value.endsWith('"')) ||
                                    (value.startsWith("'") && value.endsWith("'"))) {
                                    value = value.slice(1, -1);
                                }

                                // 处理数组格式
                                if (value.startsWith('[') && value.endsWith(']')) {
                                    try {
                                        value = JSON.parse(value);
                                    } catch (e) {
                                        // 如果解析失败，保持原值
                                    }
                                }
                            }

                            metadata[key] = value;
                        }
                    });

                    return { metadata, content: bodyContent };
                },

                async publish() {
                    if (!this.isGitHubConfigured) {
                        this.$message.warning('请先配置GitHub');
                        return;
                    }

                    // Gallery模式特殊处理
                    if (this.currentType === contentTypes.GALLERY) {
                        return await this.publishGalleryImage();
                    }

                    // 从编辑器获取最新的正文
                    this.bodyContent = this.vditor.getValue();

                    // 检查是否有内容（正文不能为空）
                    if (!this.bodyContent.trim()) {
                        this.$message.warning('请先编写内容');
                        return;
                    }

                    if (this.currentType === contentTypes.BLOG && !this.metadata.title) {
                        this.$message.warning('请先设置标题');
                        return;
                    }

                    const finalContent = generateContentWithMetadata(this.currentType, this.metadata, this.bodyContent);

                    // 获取发布按钮元素
                    const publishButton = document.querySelector('.publish-button');

                    try {
                        this.$message({ message: '正在发布...', type: 'info' });

                        const result = await window.githubService.publishContent(
                            this.currentType,
                            this.metadata,
                            finalContent
                        );

                        if (result.success) {
                            // 添加成功状态动画
                            if (publishButton) {
                                publishButton.classList.add('success');

                                // 1.5秒后移除成功状态
                                setTimeout(() => {
                                    publishButton.classList.remove('success');
                                }, 1500);
                            }

                            this.$message({
                                message: `发布成功！文件已${result.action === 'add' ? '创建' : '更新'}`,
                                type: 'success'
                            });

                            this.selectType(this.currentType); // 重置
                        }
                    } catch (error) {
                        console.error('发布失败:', error);
                        this.$message.error(`发布失败: ${error.message}`);
                    }
                },

                configureGitHub() {
                    this.githubConfigVisible = true;
                },

                handleGitHubConfigSave(config) {
                    if (window.githubService) {
                        window.githubService.setConfig(config);
                        this.checkGitHubConfig();
                        this.$message.success('GitHub配置已保存');
                    }
                },

                // 电脑端自动聚焦
                setupDesktopAutoFocus() {
                    // 多次尝试聚焦，确保成功
                    const attemptFocus = (attempt = 1) => {
                        this.focusEditor();

                        // 检查是否成功聚焦
                        setTimeout(() => {
                            const editorElement = this.vditor?.vditor?.ir?.element;
                            const isEditorFocused = document.activeElement === editorElement ||
                                                  editorElement?.contains(document.activeElement);

                            // 如果没有聚焦成功且尝试次数少于5次，继续尝试
                            if (!isEditorFocused && attempt < 5) {
                                attemptFocus(attempt + 1);
                            }
                        }, 300);
                    };

                    // 立即尝试聚焦
                    setTimeout(() => attemptFocus(), 100);

                    // 再次尝试（确保编辑器完全加载）
                    setTimeout(() => attemptFocus(), 500);

                    // 最后一次尝试
                    setTimeout(() => attemptFocus(), 1000);
                },

                // 移动端自动聚焦
                setupMobileAutoFocus() {
                    if (!this.isMobileDevice()) return;

                    // 多次尝试聚焦，确保成功
                    const attemptFocus = (attempt = 1) => {
                        this.focusEditor();

                        // 检查是否成功聚焦
                        setTimeout(() => {
                            const editorElement = this.vditor?.vditor?.ir?.element;
                            const isEditorFocused = document.activeElement === editorElement ||
                                                  editorElement?.contains(document.activeElement);

                            // 如果没有聚焦成功且尝试次数少于5次，继续尝试
                            if (!isEditorFocused && attempt < 5) {
                                attemptFocus(attempt + 1);
                            }
                        }, 300);
                    };

                    // 立即尝试聚焦
                    setTimeout(() => attemptFocus(), 100);

                    // 再次尝试（确保编辑器完全加载）
                    setTimeout(() => attemptFocus(), 500);

                    // 最后一次尝试
                    setTimeout(() => attemptFocus(), 1000);
                },



                // 聚焦编辑器
                focusEditor() {
                    if (!this.vditor || !this.vditor.vditor || !this.vditor.vditor.ir) {
                        // 如果编辑器还没准备好，稍后重试
                        setTimeout(() => this.focusEditor(), 200);
                        return;
                    }

                    try {
                        // 使用Vditor的内置聚焦方法
                        this.vditor.focus();

                        // 获取编辑器的文本区域
                        const editorElement = this.vditor.vditor.ir.element;
                        if (editorElement) {
                            // 确保编辑器获得焦点
                            editorElement.focus();

                            // 触发点击事件以确保光标显示
                            const clickEvent = new MouseEvent('click', {
                                view: window,
                                bubbles: true,
                                cancelable: true
                            });
                            editorElement.dispatchEvent(clickEvent);

                            // 将光标移到内容末尾
                            setTimeout(() => {
                                const range = document.createRange();
                                const selection = window.getSelection();

                                if (editorElement.childNodes.length > 0) {
                                    // 找到最后一个文本节点
                                    let lastNode = editorElement;
                                    while (lastNode.lastChild) {
                                        lastNode = lastNode.lastChild;
                                    }

                                    if (lastNode.nodeType === Node.TEXT_NODE) {
                                        range.setStart(lastNode, lastNode.textContent.length);
                                        range.setEnd(lastNode, lastNode.textContent.length);
                                    } else {
                                        range.selectNodeContents(editorElement);
                                        range.collapse(false);
                                    }
                                } else {
                                    range.setStart(editorElement, 0);
                                    range.setEnd(editorElement, 0);
                                }

                                selection.removeAllRanges();
                                selection.addRange(range);

                                // 确保光标可见
                                editorElement.focus();
                            }, 100);

                            // 滚动到编辑器位置
                            setTimeout(() => {
                                editorElement.scrollIntoView({
                                    behavior: 'smooth',
                                    block: 'center'
                                });
                            }, 200);
                        }
                    } catch (error) {
                        console.log('聚焦编辑器失败:', error);
                        // 备用方案：直接聚焦编辑器容器
                        try {
                            const vditorContainer = document.querySelector('#vditor .vditor-ir pre');
                            if (vditorContainer) {
                                vditorContainer.focus();
                            }
                        } catch (e) {
                            console.log('备用聚焦方案也失败:', e);
                        }
                    }
                },

                // 检查图片服务配置
                checkImageServiceConfig() {
                    this.isImageServiceConfigured = window.imageService && window.imageService.isConfigured();
                },

                // 检测PWA模式
                detectPWAMode() {
                    this.isPWAMode = window.navigator.standalone === true ||
                                     window.matchMedia('(display-mode: standalone)').matches;
                },

                // 检查PWA模式下配置缺失的情况
                checkPWAConfigMissing() {
                    // 检测是否为PWA模式（保存到主屏幕）
                    const isPWA = window.navigator.standalone === true ||
                                  window.matchMedia('(display-mode: standalone)').matches;

                    if (isPWA) {
                        // 延迟检查，确保所有组件都已初始化
                        setTimeout(() => {
                            const hasImageConfig = localStorage.getItem('image-service-config');
                            const hasGitHubConfig = localStorage.getItem('github-config');

                            if (!hasImageConfig || !hasGitHubConfig) {
                                this.showPWAConfigDialog();
                            }
                        }, 1000);
                    }
                },

                showPWAConfigDialog() {
                    this.$confirm(
                        'PWA模式下需要重新配置。这是因为PWA和浏览器使用不同的存储空间。是否现在配置？',
                        'PWA配置提醒',
                        {
                            confirmButtonText: '立即配置',
                            cancelButtonText: '稍后配置',
                            type: 'info',
                            customClass: 'pwa-config-dialog'
                        }
                    ).then(() => {
                        // 优先配置图片服务
                        this.imageConfigVisible = true;
                    }).catch(() => {
                        // 用户选择稍后配置，显示提示
                        this.$message({
                            message: '提示：图片上传功能需要先配置GitHub仓库信息',
                            type: 'warning',
                            duration: 5000
                        });
                    });
                },

                // 快速配置功能（用于PWA模式）
                showQuickConfig() {
                    this.$prompt('请输入GitHub Token:', '快速配置', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        inputType: 'password',
                        inputPlaceholder: '请输入您的GitHub Personal Access Token'
                    }).then(({ value }) => {
                        if (value) {
                            this.applyQuickConfig(value);
                        }
                    }).catch(() => {
                        this.$message.info('已取消配置');
                    });
                },

                applyQuickConfig(token) {
                    // 使用预设的配置信息
                    const imageConfig = {
                        token: token,
                        owner: 'SUNSIR007',
                        repo: 'picx-images-hosting',
                        branch: 'master',
                        imageDir: 'images'
                    };

                    const githubConfig = {
                        token: token,
                        owner: 'SUNSIR007',
                        repo: 'markdown-online-editor'
                    };

                    // 保存配置
                    localStorage.setItem('image-service-config', JSON.stringify(imageConfig));
                    localStorage.setItem('github-config', JSON.stringify(githubConfig));
                    localStorage.setItem('image-service-link-rule', 'jsdelivr');

                    // 应用配置
                    if (window.imageService) {
                        window.imageService.setConfig(imageConfig);
                        window.imageService.setLinkRule('jsdelivr');
                    }
                    if (window.githubService) {
                        window.githubService.setConfig(githubConfig);
                    }

                    // 更新状态
                    this.checkGitHubConfig();
                    this.checkImageServiceConfig();

                    this.$message.success('快速配置完成！');
                },

                // 处理图片上传
                async handleImageUpload(files) {
                    if (!this.isImageServiceConfigured) {
                        this.$message.warning('请先配置图床服务');
                        return Promise.reject('图床未配置');
                    }

                    const fileArray = Array.from(files);
                    this.uploadingImages = true;
                    this.uploadProgress.visible = true;
                    this.uploadProgress.total = fileArray.length;

                    // 清除拖拽视觉效果
                    const vditorElement = document.getElementById('vditor');
                    if (vditorElement) {
                        vditorElement.classList.remove('drag-over');
                    }

                    // 移动端调试信息
                    if (this.isMobileDevice()) {
                        console.log('移动端上传开始:', {
                            fileCount: fileArray.length,
                            files: fileArray.map(f => ({ name: f.name, size: f.size, type: f.type })),
                            userAgent: navigator.userAgent,
                            imageServiceConfig: {
                                owner: window.imageService?.owner,
                                repo: window.imageService?.repo,
                                branch: window.imageService?.branch,
                                isConfigured: window.imageService?.isConfigured()
                            }
                        });
                    }

                    try {
                        const results = await window.imageService.uploadImages(fileArray, {
                            compress: true,
                            quality: 0.8,
                            addHash: true,
                            onProgress: (progress) => {
                                this.uploadProgress.current = progress.current;
                                this.uploadProgress.fileName = progress.fileName;
                                this.uploadProgress.stage = progress.stage;
                                this.uploadProgress.progress = progress.singleProgress || 0;
                                this.uploadProgress.currentSize = progress.currentSize || 0;
                                this.uploadProgress.targetSize = progress.targetSize || 0;
                                this.uploadProgress.quality = progress.quality || 0;
                            },
                            onSingleComplete: (result, index) => {
                                if (result.success) {
                                    // 插入Markdown图片语法到编辑器
                                    const imageMarkdown = `![${result.fileName}](${result.url})\n`;
                                    this.insertTextToEditor(imageMarkdown);
                                    this.$message.success(`图片 ${result.fileName} 上传成功`);
                                } else {
                                    this.$message.error(`图片 ${result.fileName} 上传失败: ${result.error}`);
                                    // 移动端额外错误信息
                                    if (this.isMobileDevice()) {
                                        console.error('移动端上传失败详情:', result);
                                    }
                                }
                            }
                        });

                        const successCount = results.filter(r => r.success).length;
                        const failCount = results.length - successCount;

                        if (successCount > 0) {
                            this.$message.success(`成功上传 ${successCount} 张图片${failCount > 0 ? `，${failCount} 张失败` : ''}`);
                        }

                        return Promise.resolve(results);

                    } catch (error) {
                        console.error('图片上传错误:', error);
                        // 移动端显示详细错误对话框
                        if (this.isMobileDevice()) {
                            this.showMobileError(error, '图片上传');
                        } else {
                            this.$message.error(`图片上传失败: ${error.message}`);
                        }
                        return Promise.reject(error);
                    } finally {
                        this.uploadingImages = false;
                        this.uploadProgress.visible = false;
                    }
                },

                // 插入文本到编辑器
                insertTextToEditor(text) {
                    if (this.vditor) {
                        this.vditor.insertValue(text);
                    }
                },

                // 检测是否为移动设备
                isMobileDevice() {
                    return /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
                },

                // 显示移动端错误详情
                showMobileError(error, context = '') {
                    if (this.isMobileDevice()) {
                        const errorInfo = {
                            时间: new Date().toLocaleString(),
                            错误: error.message || error,
                            上下文: context,
                            用户代理: navigator.userAgent,
                            URL: window.location.href,
                            图床配置: window.imageService ? {
                                owner: window.imageService.owner,
                                repo: window.imageService.repo,
                                branch: window.imageService.branch,
                                已配置: window.imageService.isConfigured()
                            } : '未初始化'
                        };

                        this.mobileErrorDialog.content = JSON.stringify(errorInfo, null, 2);
                        this.mobileErrorDialog.visible = true;
                    }
                },

                // 复制错误信息到剪贴板
                async copyErrorToClipboard() {
                    try {
                        await navigator.clipboard.writeText(this.mobileErrorDialog.content);
                        this.$message.success('错误信息已复制到剪贴板');
                    } catch (err) {
                        // 降级方案
                        const textArea = document.createElement('textarea');
                        textArea.value = this.mobileErrorDialog.content;
                        document.body.appendChild(textArea);
                        textArea.select();
                        document.execCommand('copy');
                        document.body.removeChild(textArea);
                        this.$message.success('错误信息已复制到剪贴板');
                    }
                },

                // 设置图片拖拽和粘贴支持
                setupImageDragAndPaste() {
                    const vditorElement = document.getElementById('vditor');
                    if (!vditorElement) return;

                    // 拖拽支持
                    vditorElement.addEventListener('dragover', (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        vditorElement.classList.add('drag-over');
                    });

                    vditorElement.addEventListener('dragleave', (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        vditorElement.classList.remove('drag-over');
                    });

                    vditorElement.addEventListener('drop', (e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        vditorElement.classList.remove('drag-over');

                        const files = Array.from(e.dataTransfer.files).filter(file =>
                            file.type.startsWith('image/')
                        );

                        if (files.length > 0) {
                            this.handleImageUpload(files);
                        }
                    });

                    // 粘贴支持
                    vditorElement.addEventListener('paste', (e) => {
                        const items = Array.from(e.clipboardData.items);
                        const imageItems = items.filter(item => item.type.startsWith('image/'));

                        if (imageItems.length > 0) {
                            e.preventDefault();
                            const files = imageItems.map(item => item.getAsFile()).filter(file => file);
                            if (files.length > 0) {
                                this.handleImageUpload(files);
                            }
                        }
                    });
                },

                // 配置图片服务
                configureImageService() {
                    this.imageConfigVisible = true;
                },

                // 处理图片服务配置保存
                handleImageServiceConfigSave(config) {
                    if (window.imageService) {
                        window.imageService.setConfig(config);
                        this.checkImageServiceConfig();
                        this.$message.success('图床配置已保存');
                    }
                },

                // Gallery相关方法
                resetGalleryPreview() {
                    this.galleryPreview = {
                        url: null,
                        fileName: '',
                        size: 0,
                        file: null
                    };
                },

                // 获取阶段文本
                getStageText(stage) {
                    const stageTexts = {
                        'preparing': '准备中...',
                        'analyzing': '分析图片...',
                        'compressing': '智能压缩中...',
                        'converting': '转换中...',
                        'uploading': '上传中...',
                        'generating': '生成链接...',
                        'finalizing': '完成处理...',
                        'completed': '完成'
                    };
                    return stageTexts[stage] || '处理中...';
                },

                // 格式化文件大小
                formatFileSize(bytes) {
                    if (bytes === 0) return '0 Bytes';
                    const k = 1024;
                    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                    const i = Math.floor(Math.log(bytes) / Math.log(k));
                    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
                },

                async publishGalleryImage() {
                    // 从编辑器内容中提取图片URL
                    const content = this.vditor.getValue();
                    const imageUrlMatch = content.match(/!\[.*?\]\((https?:\/\/[^\)]+)\)/);

                    if (!imageUrlMatch) {
                        this.$message.warning('请先在编辑器中上传图片');
                        return;
                    }

                    const imageUrl = imageUrlMatch[1];

                    const publishButton = document.querySelector('.publish-button');

                    try {
                        this.$message({ message: '正在保存图片信息...', type: 'info' });

                        // 创建图片数据
                        const imageData = {
                            url: imageUrl,
                            thumbnail: imageUrl, // 使用同一个URL作为缩略图
                            date: this.metadata.date
                        };

                        // 生成文件名（使用时间戳）
                        const fileName = this.generateGalleryFileName();
                        const filePath = `src/content/photos/${fileName}.json`;

                        // 保存JSON文件到GitHub
                        const jsonContent = JSON.stringify(imageData, null, 2);
                        const result = await window.githubService.createOrUpdateFile(
                            filePath,
                            jsonContent,
                            `Add gallery image: ${fileName}`
                        );

                        if (publishButton) {
                            publishButton.classList.add('success');
                            setTimeout(() => {
                                publishButton.classList.remove('success');
                            }, 1500);
                        }

                        this.$message({
                            message: '图片发布成功！',
                            type: 'success'
                        });

                        this.selectType(this.currentType); // 重置

                    } catch (error) {
                        console.error('发布失败:', error);
                        this.$message.error(`发布失败: ${error.message}`);
                    }
                },

                generateGalleryFileName() {
                    // 使用时间戳生成文件名
                    const now = new Date();
                    const timestamp = now.getTime();
                    const dateStr = now.toISOString().split('T')[0]; // YYYY-MM-DD
                    return `photo-${dateStr}-${timestamp}`;
                }
            }
        });

        }); // 结束DOMContentLoaded事件监听器
    </script>
</body>
</html>
